'use client'
import React, { memo } from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Badge,
  Spinner,
  Grid,
  Button,
  Icon,
  Dialog
} from '@chakra-ui/react';
import {
  DialogRoot,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogBody,
  DialogFooter,
  DialogCloseTrigger
} from '@chakra-ui/react';

import { useBidHistoryQuery, useBidActivityQuery } from '@/services/useBidHistoryQuery';
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext';
import { formatDistanceToNowJakarta } from '@/utils/timezone';
import { FaTrophy, FaClock, FaUsers, FaChartLine, FaGavel, FaCrown } from 'react-icons/fa';

interface BidHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
}

const BidHistoryModal: React.FC<BidHistoryModalProps> = memo(({
  isOpen,
  onClose,
  productId,
  productName
}) => {
  const { formatPrice, currency } = useCurrencyLanguage();
  const { data: bidHistoryData, isLoading: isLoadingHistory } = useBidHistoryQuery(productId, 1, 20);
  const { data: activityData, isLoading: isLoadingActivity } = useBidActivityQuery(productId);

  const isLoading = isLoadingHistory || isLoadingActivity;
  const bids = bidHistoryData?.bidHistory || [];
  const stats = bidHistoryData?.stats;
  const totalBids = stats?.totalBids || 0;
  const highestBid = stats?.currentHighestBid || 0;
  const uniqueBidders = stats?.uniqueBidders || 0;

  return (
    <Dialog.Root open={isOpen} onOpenChange={(details) => !details.open && onClose()}>
      <DialogContent maxW="4xl" maxH="80vh">
        <DialogHeader>
          <DialogTitle>
            <HStack gap={3}>
              <Icon as={FaGavel} color="blue.500" />
              <VStack align="start" gap={0}>
                <Text fontSize="lg" fontWeight="bold">Bid History</Text>
                <Text fontSize="sm" color="gray.600" fontWeight="normal">
                  {productName}
                </Text>
              </VStack>
            </HStack>
          </DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody p={0}>
          {isLoading ? (
            <VStack py={12} gap={4}>
              <Spinner size="xl" color="blue.500" />
              <Text color="gray.600">Loading bid history...</Text>
            </VStack>
          ) : (
            <VStack gap={0} align="stretch">
              {/* Stats Header */}
              <Box p={6} bg="gray.50" borderBottom="1px" borderColor="gray.200">
                <Grid templateColumns="repeat(3, 1fr)" gap={6}>
                  <VStack gap={1}>
                    <HStack gap={2}>
                      <Icon as={FaGavel} color="blue.500" />
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">
                        Total Bids
                      </Text>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                      {totalBids}
                    </Text>
                  </VStack>

                  <VStack gap={1}>
                    <HStack gap={2}>
                      <Icon as={FaCrown} color="yellow.500" />
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">
                        Highest Bid
                      </Text>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600">
                      {formatPrice(highestBid, currency)}
                    </Text>
                  </VStack>

                  <VStack gap={1}>
                    <HStack gap={2}>
                      <Icon as={FaUsers} color="purple.500" />
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">
                        Bidders
                      </Text>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                      {uniqueBidders}
                    </Text>
                  </VStack>
                </Grid>
              </Box>

              {/* Bid List */}
              <Box maxH="400px" overflowY="auto">
                {bids && bids.length > 0 ? (
                  <VStack gap={0} align="stretch">
                    {bids.map((bid, index) => (
                      <Box
                        key={bid.id}
                        p={4}
                        borderBottom="1px"
                        borderColor="gray.100"
                        _hover={{ bg: "gray.50" }}
                        bg={bid.isWinning ? "green.50" : "white"}
                      >
                        <HStack justify="space-between" align="center">
                          <HStack gap={4}>
                            <Box
                              w="32px"
                              h="32px"
                              borderRadius="full"
                              bg="blue.100"
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                              fontSize="sm"
                              fontWeight="bold"
                              color="blue.600"
                            >
                              {bid.bidder.name.charAt(0).toUpperCase()}
                            </Box>
                            <VStack align="start" gap={1}>
                              <HStack gap={2}>
                                <Text fontWeight="medium" fontSize="sm">
                                  {bid.bidder.name}
                                </Text>
                                {bid.isWinning && (
                                  <Badge colorScheme="green" size="sm">
                                    <Icon as={FaCrown} mr={1} />
                                    Leading
                                  </Badge>
                                )}
                              </HStack>
                              <Text fontSize="xs" color="gray.600">
                                {formatDistanceToNowJakarta(bid.bidTime)}
                              </Text>
                            </VStack>
                          </HStack>

                          <VStack align="end" gap={1}>
                            <Text
                              fontSize="lg"
                              fontWeight="bold"
                              color={bid.isWinning ? "green.600" : "gray.700"}
                            >
                              {formatPrice(bid.amount, currency)}
                            </Text>
                            <Text fontSize="xs" color="gray.500">
                              Bid #{bid.bidNumber}
                            </Text>
                          </VStack>
                        </HStack>
                      </Box>
                    ))}
                  </VStack>
                ) : (
                  <VStack py={12} gap={4}>
                    <Icon as={FaClock} boxSize={12} color="gray.400" />
                    <VStack gap={2}>
                      <Text fontSize="lg" color="gray.600" fontWeight="medium">
                        No bids yet
                      </Text>
                      <Text fontSize="sm" color="gray.500" textAlign="center">
                        Be the first to place a bid on this item!
                      </Text>
                    </VStack>
                  </VStack>
                )}
              </Box>
            </VStack>
          )}
        </DialogBody>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog.Root>
  );
});

BidHistoryModal.displayName = 'BidHistoryModal';

export default BidHistoryModal;
