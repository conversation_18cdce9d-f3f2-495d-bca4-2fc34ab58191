export interface ProductItem {
    id: string | number;
    image: string;
    images?: string[]; // Multiple images for gallery
    title: string;
    price: string;
    bids?: string | number;
    slug?: string;
    timeLeft?: string;
    type?: string; // e.g., 'auction', 'buyNow'
    description?: string; // Optional description field
}

// Import Product type from useProductQuery
export type { Product, ProductsResponse, ProductQueryParams } from '@/services/useProductQuery';
  