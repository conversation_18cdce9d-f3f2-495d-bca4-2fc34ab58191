import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";
import { getJakartaTime, toJakartaTime } from "@/utils/timezone";

// Enhanced bidding status types
export type BiddingStatus = 'active' | 'ended' | 'won' | 'lost' | 'cancelled';
export type OrderStatus = 'pending_payment' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded' | 'expired';

// Enhanced bidding item with comprehensive status
export interface BiddingItemStatus {
  id: string;
  productId: string;
  productName: string;
  productSlug?: string;
  productImage?: string;
  
  // Bidding information
  biddingStatus: BiddingStatus;
  isWinning: boolean;
  highestBid: number;
  currentBid: number;
  totalBids: number;
  lastBidTime: string;
  auctionEndDate?: string;
  
  // Order information (if won)
  orderId?: string;
  orderNumber?: string;
  orderStatus?: OrderStatus;
  paymentStatus?: PaymentStatus;
  trackingNumber?: string;
  
  // Timeline and progress
  statusTimeline: StatusTimelineItem[];
  progressPercentage: number;
  nextAction?: NextAction;
  
  // Deadlines and urgency
  paymentDeadline?: string;
  isUrgent: boolean;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  urgencyMessage?: string;
}

export interface StatusTimelineItem {
  id: string;
  status: string;
  title: string;
  description: string;
  timestamp: string;
  isCompleted: boolean;
  isCurrent: boolean;
  icon: string;
  color: string;
}

export interface NextAction {
  type: 'pay' | 'wait' | 'track' | 'contact' | 'review';
  title: string;
  description: string;
  buttonText: string;
  buttonColor: string;
  url?: string;
  isUrgent: boolean;
}

export interface BiddingStatusSummary {
  totalBids: number;
  activeBids: number;
  wonAuctions: number;
  pendingPayments: number;
  activeOrders: number;
  completedOrders: number;
  totalSpent: number;
  averageBid: number;
  winRate: number;
  urgentActions: number;
}

// Query Keys
export const biddingStatusQueryKeys = {
  all: ['bidding-status'] as const,
  summary: () => [...biddingStatusQueryKeys.all, 'summary'] as const,
  items: () => [...biddingStatusQueryKeys.all, 'items'] as const,
  itemsList: (params: any) => [...biddingStatusQueryKeys.items(), params] as const,
  item: (productId: string) => [...biddingStatusQueryKeys.all, 'item', productId] as const,
};

// Custom hook for fetching bidding status summary
export const useBiddingStatusSummaryQuery = () => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingStatusQueryKeys.summary(),
    queryFn: async (): Promise<BiddingStatusSummary> => {
      const response = await api.get('/bidding/status/summary');
      return response.data.data;
    },
    enabled: !!api,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Custom hook for fetching enhanced bidding items with status
export const useBiddingItemsStatusQuery = (params: {
  page?: number;
  limit?: number;
  status?: string;
  urgencyLevel?: string;
  sortBy?: string;
  sortOrder?: string;
} = {}) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingStatusQueryKeys.itemsList(params),
    queryFn: async (): Promise<{
      items: BiddingItemStatus[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }> => {
      const searchParams = new URLSearchParams();
      
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.urgencyLevel) searchParams.append('urgencyLevel', params.urgencyLevel);
      if (params.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

      const response = await api.get(`/bidding/status/items?${searchParams.toString()}`);
      return response.data.data;
    },
    enabled: !!api,
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });
};

// Custom hook for fetching single bidding item status
export const useBiddingItemStatusQuery = (productId: string) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingStatusQueryKeys.item(productId),
    queryFn: async (): Promise<BiddingItemStatus> => {
      const response = await api.get(`/bidding/status/item/${productId}`);
      return response.data.data;
    },
    enabled: !!api && !!productId,
    refetchInterval: 15000, // Refetch every 15 seconds for real-time updates
  });
};

// Custom hook for updating bidding item status (admin/system use)
export const useUpdateBiddingStatusMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      productId, 
      status, 
      orderStatus, 
      paymentStatus,
      notes 
    }: { 
      productId: string; 
      status?: BiddingStatus;
      orderStatus?: OrderStatus;
      paymentStatus?: PaymentStatus;
      notes?: string;
    }): Promise<BiddingItemStatus> => {
      const response = await api.put(`/bidding/status/item/${productId}`, {
        status,
        orderStatus,
        paymentStatus,
        notes
      });
      return response.data.data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.item(variables.productId) });
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.items() });
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.summary() });
      
      toaster.create({
        title: "Status Updated",
        description: "Bidding item status has been updated successfully",
        type: "success",
        duration: 3000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Update Status",
        description: error.response?.data?.message || "An error occurred while updating the status",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for marking urgent actions as acknowledged
export const useAcknowledgeUrgentActionMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      productId, 
      actionType 
    }: { 
      productId: string; 
      actionType: string;
    }): Promise<void> => {
      await api.post(`/bidding/status/item/${productId}/acknowledge`, {
        actionType
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.item(variables.productId) });
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.items() });
      queryClient.invalidateQueries({ queryKey: biddingStatusQueryKeys.summary() });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Acknowledge Action",
        description: error.response?.data?.message || "An error occurred",
        type: "error",
        duration: 3000,
      });
    },
  });
};

// Utility functions for status management
export const getStatusColor = (status: BiddingStatus | OrderStatus | PaymentStatus): string => {
  switch (status) {
    case 'active': return 'blue';
    case 'won': return 'green';
    case 'lost': return 'red';
    case 'ended': return 'gray';
    case 'cancelled': return 'red';
    case 'pending_payment': return 'orange';
    case 'paid': return 'green';
    case 'processing': return 'blue';
    case 'shipped': return 'purple';
    case 'delivered': return 'green';
    case 'pending': return 'orange';
    case 'failed': return 'red';
    case 'refunded': return 'gray';
    case 'expired': return 'red';
    default: return 'gray';
  }
};

export const getUrgencyColor = (level: 'low' | 'medium' | 'high' | 'critical'): string => {
  switch (level) {
    case 'low': return 'green';
    case 'medium': return 'yellow';
    case 'high': return 'orange';
    case 'critical': return 'red';
    default: return 'gray';
  }
};

export const formatTimeRemaining = (deadline: string): string => {
  const now = getJakartaTime();
  const deadlineDate = toJakartaTime(deadline);
  const diff = deadlineDate.getTime() - now.getTime();

  if (diff <= 0) return 'Expired';

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) return `${days}d ${hours}h`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
};

export default {
  useBiddingStatusSummaryQuery,
  useBiddingItemsStatusQuery,
  useBiddingItemStatusQuery,
  useUpdateBiddingStatusMutation,
  useAcknowledgeUrgentActionMutation,
  getStatusColor,
  getUrgencyColor,
  formatTimeRemaining,
};
