import { Product, ProductItem } from '@/types/product';
import { formatDistanceToNowJakarta } from '@/utils/timezone';
import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext';

/**
 * Convert Product data from API to ProductItem format for ProductCard
 */
export const convertProductToProductItem = (
  product: Product,
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string,
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number,
  currency: SupportedCurrency = 'USD'
): ProductItem => {
  // Get main image or first image
  const mainImage = product.images?.find(img => img.isMain) || product.images?.[0];
  const imageUrl = mainImage?.imageUrl || '/assets/images/placeholder.jpg';
  
  // Get all image URLs
  const imageUrls = product.images?.map(img => img.imageUrl) || [];
  
  // Convert and format price
  let displayPrice: string;
  try {
    const priceAmount = safeNumberConversion(product.priceUSD);

    if (priceAmount === 0 && product.priceUSD !== 0) {
      console.warn(`Invalid price for product ${product.id}:`, product.priceUSD);
      displayPrice = 'Price not available';
    } else {
      // Check if auction has ended
      const isAuctionEnded = product.sellType === 'auction' &&
                            product.auctionEndDate &&
                            new Date(product.auctionEndDate) <= new Date();

      // For auction items, show current bid if available and auction is active
      if (product.sellType === 'auction' && product.currentBid && product.currentBid > 0) {
        const currentBidAmount = safeNumberConversion(product.currentBid);
        if (currentBidAmount > 0) {
          try {
            console.log(`Converting current bid for product ${product.id}: ${currentBidAmount} USD to ${currency}`);
            const convertedBid = safeCurrencyConversion(currentBidAmount, convertPrice, 'USD', currency);
            console.log(`Converted bid result: ${convertedBid}`);

            // For ended auctions, show "Final Bid: $X" or "Winning Bid: $X"
            const priceLabel = isAuctionEnded ? 'Final: ' : '';
            displayPrice = priceLabel + formatPrice(convertedBid, currency);
            console.log(`Final display price for product ${product.id}: ${displayPrice}`);
          } catch (conversionError) {
            console.error(`Error converting current bid for product ${product.id}:`, conversionError);
            // Fallback to starting price
            const convertedPrice = safeCurrencyConversion(priceAmount, convertPrice, 'USD', currency);
            displayPrice = formatPrice(convertedPrice, currency);
          }
        } else {
          // No valid current bid, use starting price
          const convertedPrice = convertPrice(priceAmount, 'USD', currency);
          displayPrice = formatPrice(convertedPrice, currency);
        }
      } else {
        // Not an auction or no current bid, use starting price
        try {
          const convertedPrice = safeCurrencyConversion(priceAmount, convertPrice, 'USD', currency);
          displayPrice = formatPrice(convertedPrice, currency);
        } catch (conversionError) {
          console.error(`Error converting price for product ${product.id}:`, conversionError);
          displayPrice = 'Price not available';
        }
      }
    }
  } catch (error) {
    console.error(`Error processing price for product ${product.id}:`, error);
    displayPrice = 'Price not available';
  }
  
  // Calculate time left for auctions
  let timeLeft: string | undefined;
  if (product.sellType === 'auction' && product.auctionEndDate) {
    try {
      const endDate = new Date(product.auctionEndDate);
      const now = new Date();
      
      if (endDate > now) {
        timeLeft = formatDistanceToNowJakarta(endDate);
      } else {
        timeLeft = 'Ended';
      }
    } catch (error) {
      console.error(`Error calculating time left for product ${product.id}:`, error);
      timeLeft = 'Time not available';
    }
  }
  
  // Get bid count
  let bidCount: string | number = '0';
  try {
    if (product.bidCount !== undefined && product.bidCount !== null) {
      bidCount = typeof product.bidCount === 'number' ? product.bidCount : Number(product.bidCount);
      if (isNaN(bidCount)) {
        bidCount = product._count?.bids || 0;
      }
    } else if (product._count?.bids !== undefined) {
      bidCount = product._count.bids;
    } else if (product.bids && Array.isArray(product.bids)) {
      bidCount = product.bids.length;
    }
  } catch (error) {
    console.error(`Error getting bid count for product ${product.id}:`, error);
    bidCount = '0';
  }
  
  return {
    id: product.id,
    image: imageUrl,
    images: imageUrls,
    title: product.itemName || 'Untitled Product',
    price: displayPrice,
    bids: bidCount,
    slug: product.slug,
    timeLeft,
    type: product.sellType === 'auction' ? 'auction' : 'buyNow',
    description: product.description,
  };
};

/**
 * Convert array of Products to ProductItems
 */
export const convertProductsToProductItems = (
  products: Product[],
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string,
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number,
  currency: SupportedCurrency = 'USD'
): ProductItem[] => {
  return products.map(product => 
    convertProductToProductItem(product, formatPrice, convertPrice, currency)
  );
};

/**
 * Safe number conversion with fallback
 */
export const safeNumberConversion = (value: any, fallback: number = 0): number => {
  if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
    return value;
  }

  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) || !isFinite(parsed) ? fallback : parsed;
  }

  return fallback;
};

/**
 * Safe currency conversion with validation
 */
export const safeCurrencyConversion = (
  amount: number,
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number,
  fromCurrency: SupportedCurrency = 'USD',
  toCurrency: SupportedCurrency = 'USD'
): number => {
  try {
    // Validate input amount
    if (!isFinite(amount) || isNaN(amount)) {
      console.warn(`Invalid amount for conversion: ${amount}`);
      return 0;
    }

    // If same currency, return original amount
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Perform conversion
    const converted = convertPrice(amount, fromCurrency, toCurrency);

    // Validate result
    if (!isFinite(converted) || isNaN(converted)) {
      console.error(`Currency conversion resulted in invalid number:`, {
        amount,
        fromCurrency,
        toCurrency,
        converted
      });
      return amount; // Return original amount as fallback
    }

    return converted;
  } catch (error) {
    console.error(`Error in currency conversion:`, error);
    return amount; // Return original amount as fallback
  }
};

/**
 * Safe price formatting with error handling
 */
export const safePriceFormat = (
  amount: any,
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string,
  currency: SupportedCurrency = 'USD',
  fallback: string = 'Price not available'
): string => {
  try {
    const numericAmount = safeNumberConversion(amount);
    if (numericAmount === 0 && amount !== 0) {
      return fallback;
    }

    // Additional validation before formatting
    if (!isFinite(numericAmount) || isNaN(numericAmount)) {
      console.warn(`Invalid amount for price formatting: ${amount} -> ${numericAmount}`);
      return fallback;
    }

    return formatPrice(numericAmount, currency);
  } catch (error) {
    console.error('Error formatting price:', error);
    return fallback;
  }
};

/**
 * Get display price for product (current bid or starting price)
 */
export const getDisplayPrice = (
  product: Product,
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string,
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number,
  currency: SupportedCurrency = 'USD'
): string => {
  try {
    // Check if auction has ended
    const isAuctionEnded = product.sellType === 'auction' &&
                          product.auctionEndDate &&
                          new Date(product.auctionEndDate) <= new Date();

    // For auction items, prioritize current bid if available
    if (product.sellType === 'auction' && product.currentBid && product.currentBid > 0) {
      const currentBidAmount = safeNumberConversion(product.currentBid);
      if (currentBidAmount > 0) {
        try {
          const convertedBid = safeCurrencyConversion(currentBidAmount, convertPrice, 'USD', currency);

          // For ended auctions, add label
          const priceLabel = isAuctionEnded ? 'Final: ' : '';
          return priceLabel + formatPrice(convertedBid, currency);
        } catch (conversionError) {
          console.error(`Error converting current bid for product ${product.id}:`, conversionError);
          // Fallback to starting price
        }
      }
    }

    // Fall back to starting price
    const priceAmount = safeNumberConversion(product.priceUSD);
    try {
      const convertedPrice = safeCurrencyConversion(priceAmount, convertPrice, 'USD', currency);
      return formatPrice(convertedPrice, currency);
    } catch (conversionError) {
      console.error(`Error converting starting price for product ${product.id}:`, conversionError);
      return 'Price not available';
    }
  } catch (error) {
    console.error(`Error getting display price for product ${product.id}:`, error);
    return 'Price not available';
  }
};

/**
 * Get bid count with fallback
 */
export const getBidCount = (product: Product): number => {
  try {
    // Try bidCount first
    if (product.bidCount !== undefined && product.bidCount !== null) {
      const count = safeNumberConversion(product.bidCount);
      if (count >= 0) return count;
    }
    
    // Try _count.bids
    if (product._count?.bids !== undefined) {
      return safeNumberConversion(product._count.bids);
    }
    
    // Try bids array length
    if (product.bids && Array.isArray(product.bids)) {
      return product.bids.length;
    }
    
    return 0;
  } catch (error) {
    console.error(`Error getting bid count for product ${product.id}:`, error);
    return 0;
  }
};

/**
 * Check if product is auction and active
 */
export const isActiveAuction = (product: Product): boolean => {
  if (product.sellType !== 'auction') return false;
  
  if (!product.auctionEndDate) return false;
  
  try {
    const endDate = new Date(product.auctionEndDate);
    const now = new Date();
    return endDate > now;
  } catch (error) {
    console.error(`Error checking auction status for product ${product.id}:`, error);
    return false;
  }
};

/**
 * Get auction time remaining
 */
export const getAuctionTimeLeft = (product: Product): string | null => {
  if (!isActiveAuction(product)) return null;
  
  try {
    const endDate = new Date(product.auctionEndDate!);
    return formatDistanceToNowJakarta(endDate);
  } catch (error) {
    console.error(`Error calculating time left for product ${product.id}:`, error);
    return 'Time not available';
  }
};
