import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/id'; // Indonesian locale

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

// Set default locale to Indonesian
dayjs.locale('id');

// Jakarta timezone utilities
export const JAKARTA_TIMEZONE = 'Asia/Jakarta';

/**
 * Get current time in Jakarta timezone
 */
export const getJakartaTime = (): Date => {
  return dayjs().tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Convert any date to Jakarta timezone
 */
export const toJakartaTime = (date: Date | string): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Format date in Jakarta timezone
 */
export const formatJakartaDate = (
  date: Date | string,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).format(format);
};

/**
 * Format date in Jakarta timezone with Indonesian locale
 */
export const formatJakartaDateID = (
  date: Date | string,
  format: string = 'DD MMMM YYYY HH:mm'
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).locale('id').format(format);
};

/**
 * Get time difference in Jakarta timezone (in milliseconds)
 */
export const getTimeDifferenceJakarta = (
  startDate: Date | string,
  endDate: Date | string = new Date()
): number => {
  const start = dayjs(startDate).tz(JAKARTA_TIMEZONE);
  const end = dayjs(endDate).tz(JAKARTA_TIMEZONE);
  return end.diff(start);
};

/**
 * Check if a date is in the past (Jakarta timezone)
 */
export const isDatePastJakarta = (date: Date | string): boolean => {
  const inputDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const now = dayjs().tz(JAKARTA_TIMEZONE);
  return inputDate.isBefore(now);
};

/**
 * Check if a date is in the future (Jakarta timezone)
 */
export const isDateFutureJakarta = (date: Date | string): boolean => {
  const inputDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const now = dayjs().tz(JAKARTA_TIMEZONE);
  return inputDate.isAfter(now);
};

/**
 * Add time to a date in Jakarta timezone
 */
export const addTimeJakarta = (
  date: Date | string,
  amount: number,
  unit: dayjs.ManipulateType = 'millisecond'
): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).add(amount, unit).toDate();
};

/**
 * Get start of day in Jakarta timezone
 */
export const getStartOfDayJakarta = (date: Date | string = new Date()): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).startOf('day').toDate();
};

/**
 * Get end of day in Jakarta timezone
 */
export const getEndOfDayJakarta = (date: Date | string = new Date()): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).endOf('day').toDate();
};

/**
 * Convert Jakarta time to UTC
 */
export const jakartaToUTC = (date: Date | string): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).utc().toDate();
};

/**
 * Convert UTC to Jakarta time
 */
export const utcToJakarta = (date: Date | string): Date => {
  return dayjs.utc(date).tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Format distance to now in Jakarta timezone using Day.js
 */
export const formatDistanceToNowJakarta = (
  date: Date | string,
  withoutSuffix: boolean = false
): string => {
  const jakartaDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const jakartaNow = dayjs().tz(JAKARTA_TIMEZONE);
  return jakartaDate.from(jakartaNow, withoutSuffix);
};

/**
 * Format date in Jakarta timezone using Day.js
 */
export const formatJakarta = (
  date: Date | string,
  formatStr: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).format(formatStr);
};

/**
 * Add days to a date in Jakarta timezone using Day.js
 */
export const addDaysJakarta = (
  date: Date | string,
  amount: number
): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).add(amount, 'day').toDate();
};

/**
 * Get relative time from now in Jakarta timezone
 */
export const fromNowJakarta = (
  date: Date | string,
  withoutSuffix: boolean = false
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).fromNow(withoutSuffix);
};

/**
 * Get relative time to another date in Jakarta timezone
 */
export const toJakarta = (
  date: Date | string,
  compareDate: Date | string,
  withoutSuffix: boolean = false
): string => {
  const jakartaDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const jakartaCompareDate = dayjs(compareDate).tz(JAKARTA_TIMEZONE);
  return jakartaDate.to(jakartaCompareDate, withoutSuffix);
};
