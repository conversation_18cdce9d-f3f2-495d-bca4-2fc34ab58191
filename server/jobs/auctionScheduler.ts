import cron from 'node-cron';
import notificationService from '../services/notification.service';
import webSocketService from '../services/websocket.service';
import { successResponse, errorResponse } from '../utils/response.util';
import { prisma } from '../db';
import { getJakartaTime, formatJakartaDate } from '../utils/timezone.util';
import { getCurrentISOString, formatDateForDB } from '../utils/database.util';

interface AuctionWinner {
  productId: string;
  productTitle: string;
  productSlug: string;
  productImage: string;
  winnerId: string;
  winnerEmail: string;
  winnerName: string;
  winningBid: number;
  auctionEndDate: string;
  bidId: string;
}

interface ProcessedAuction {
  productId: string;
  title: string;
  endDate: Date | null;
  hasBids: boolean;
  winningBid: number;
}



class AuctionScheduler {
  private isRunning = false;
  private checkInterval = '*/2 * * * *'; // Every 2 minutes

  /**
   * Start the auction scheduler
   */
  start() {
    console.log('Starting auction end detection scheduler...');

    // Check for ended auctions every 2 minutes
    cron.schedule(this.checkInterval, async () => {
      if (this.isRunning) {
        console.log('Auction check already running, skipping...');
        return;
      }

      this.isRunning = true;
      try {
        await this.checkEndedAuctions();
      } catch (error) {
        console.error('❌ Error during auction check:', error);
      } finally {
        this.isRunning = false;
      }
    }, {
      timezone: "Asia/Jakarta"
    });

    // Run initial check after 30 seconds
    setTimeout(() => {
      this.runInitialCheck();
    }, 30000);

    console.log('✅ Auction scheduler started successfully');
    console.log('🔄 Checking for ended auctions every 2 minutes');
  }

  /**
   * Run initial check on startup
   */
  private async runInitialCheck() {
    if (this.isRunning) {
      console.log('Auction check already running, skipping initial check...');
      return;
    }

    this.isRunning = true;
    console.log('Running initial auction end check...');

    try {
      await this.checkEndedAuctions();
      console.log('✅ Initial auction check completed');
    } catch (error) {
      console.error('❌ Error during initial auction check:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Check for auctions that have ended and process winners
   */
  async checkEndedAuctions(): Promise<any> {
    try {
      // Get current time in Jakarta timezone as ISO string
      const nowISO = getCurrentISOString();

      // Optimized query to find ended auctions with selective fields
      const endedAuctions = await prisma.product.findMany({
        where: {
          sellType: 'auction',
          auctionEndDate: {
            lte: nowISO
          },
          // Only process auctions that haven't been marked as completed
          auctionCompleted: false,
          // Only process active auctions
          isActive: true,
          status: 'active'
        },
        select: {
          id: true,
          itemName: true,
          auctionEndDate: true,
          sellerId: true,
          // Get only the highest bid efficiently
          bids: {
            select: {
              id: true,
              amount: true,
              bidderId: true,
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            },
            orderBy: { amount: 'desc' },
            take: 1,
          },
          // Get only main image
          images: {
            select: {
              id: true,
              imageUrl: true,
              altText: true,
            },
            where: { isMain: true },
            take: 1,
          }
        },
        // Limit batch size to prevent overwhelming the system
        take: 50,
      });

      if (endedAuctions.length === 0) {
        console.log('No ended auctions to process');
        return successResponse('No ended auctions found');
      }

      console.log(`Found ${endedAuctions.length} ended auction(s) to process`);

      const processedAuctions: ProcessedAuction[] = [];
      const winners: AuctionWinner[] = [];

      // Process auctions in batches to avoid overwhelming the database
      const batchSize = 5;
      for (let i = 0; i < endedAuctions.length; i += batchSize) {
        const batch = endedAuctions.slice(i, i + batchSize);

        await Promise.allSettled(
          batch.map(async (auction) => {
            try {
              // Use transaction to ensure data consistency
              const result = await prisma.$transaction(async (tx) => {
                // Mark auction as completed first to prevent duplicate processing
                await tx.product.update({
                  where: { id: auction.id },
                  data: {
                    auctionCompleted: true,
                    auctionCompletedAt: getCurrentISOString()
                  }
                });

                if (auction.bids.length > 0) {
                  const winningBid = auction.bids[0];
                  const winner = winningBid.bidder;

                  // Mark the winning bid
                  await tx.bid.update({
                    where: { id: winningBid.id },
                    data: {
                      isWinning: true,
                      auctionWon: true
                    }
                  });

                  // Mark all other bids as not winning in a single query
                  await tx.bid.updateMany({
                    where: {
                      productId: auction.id,
                      id: { not: winningBid.id }
                    },
                    data: {
                      isWinning: false,
                      auctionWon: false
                    }
                  });

                  return {
                    hasWinner: true,
                    winningBid,
                    winner
                  };
                }

                return { hasWinner: false };
              });

              // Process winner data outside transaction
              if (result.hasWinner && result.winningBid && result.winner) {
                const winnerData: AuctionWinner = {
                  productId: auction.id,
                  productTitle: auction.itemName,
                  productSlug: auction.slug || '',
                  productImage: auction.images[0]?.imageUrl || '',
                  winnerId: result.winner.id,
                  winnerEmail: result.winner.email || '',
                  winnerName: `${result.winner.firstName || ''} ${result.winner.lastName || ''}`.trim(),
                  winningBid: Number(result.winningBid.amount),
                  auctionEndDate: auction.auctionEndDate?.toISOString() || '',
                  bidId: result.winningBid.id
                };

                winners.push(winnerData);
                console.log(`✅ Processed auction: ${auction.itemName} - Winner: ${winnerData.winnerName} ($${result.winningBid.amount})`);
              } else {
                console.log(`⚠️ Auction ended with no bids: ${auction.itemName}`);
              }

              processedAuctions.push({
                productId: auction.id,
                title: auction.itemName,
                endDate: auction.auctionEndDate,
                hasBids: auction.bids.length > 0,
                winningBid: Number(auction.bids[0]?.amount || 0)
              });

            } catch (error) {
              console.error(`❌ Error processing auction ${auction.id}:`, error);
              // Revert the completion status if processing failed
              try {
                await prisma.product.update({
                  where: { id: auction.id },
                  data: { auctionCompleted: false }
                });
              } catch (revertError) {
                console.error(`❌ Failed to revert auction completion status for ${auction.id}:`, revertError);
              }
            }
          })
        );

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < endedAuctions.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Send winner notifications in parallel (but with rate limiting)
      if (winners.length > 0) {
        await this.notifyWinners(winners);
      }

      // Send WebSocket notifications about auction completions in batches
      if (processedAuctions.length > 0) {
        const notificationPromises = processedAuctions.map(auction =>
          Promise.resolve().then(() => {
            webSocketService.notifyAuctionStatusChange(auction.productId, 'ended', {
              productId: auction.productId,
              title: auction.title,
              endDate: auction.endDate,
              hasBids: auction.hasBids,
              winningBid: auction.winningBid
            });
          })
        );

        // Execute WebSocket notifications in parallel
        await Promise.allSettled(notificationPromises);
      }

      return successResponse('Auction check completed', {
        processedCount: processedAuctions.length,
        winnersCount: winners.length,
        processedAuctions
      });

    } catch (error) {
      console.error('❌ Error checking ended auctions:', error);
      return errorResponse('Failed to check ended auctions', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Send notifications to auction winners with optimized batch processing
   */
  private async notifyWinners(winners: AuctionWinner[]): Promise<void> {
    console.log(`Sending notifications to ${winners.length} auction winner(s)...`);

    // Process notifications in smaller batches to avoid overwhelming email service
    const notificationBatchSize = 3;
    const successfulNotifications: string[] = [];
    const failedNotifications: string[] = [];

    for (let i = 0; i < winners.length; i += notificationBatchSize) {
      const batch = winners.slice(i, i + notificationBatchSize);

      const batchPromises = batch.map(async (winner) => {
        try {
          // Send winner notification
          const notificationResult = await notificationService.sendAuctionWinnerNotification({
            productId: winner.productId,
            productTitle: winner.productTitle,
            productSlug: winner.productSlug,
            productImage: winner.productImage,
            winnerId: winner.winnerId,
            winnerEmail: winner.winnerEmail,
            winnerName: winner.winnerName,
            winningBid: winner.winningBid,
            auctionEndDate: winner.auctionEndDate,
            bidId: winner.bidId
          });

          if (notificationResult.status) {
            console.log(`✅ Winner notification sent successfully to: ${winner.winnerEmail}`);
            successfulNotifications.push(winner.winnerEmail);

            // Send auction ended notifications to losing bidders
            try {
              await notificationService.sendAuctionEndedNotifications(winner.productId);
            } catch (endedNotificationError) {
              console.error(`❌ Failed to send auction ended notifications for product ${winner.productId}:`, endedNotificationError);
            }
          } else {
            console.error(`❌ Failed to send winner notification to ${winner.winnerEmail}:`, notificationResult.message);
            failedNotifications.push(winner.winnerEmail);
          }

        } catch (error) {
          console.error(`❌ Error notifying winner ${winner.winnerEmail}:`, error);
          failedNotifications.push(winner.winnerEmail);
        }
      });

      // Wait for current batch to complete
      await Promise.allSettled(batchPromises);

      // Rate limiting: delay between batches
      if (i + notificationBatchSize < winners.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    console.log(`📊 Notification summary: ${successfulNotifications.length} successful, ${failedNotifications.length} failed`);
    if (failedNotifications.length > 0) {
      console.error(`❌ Failed notifications for: ${failedNotifications.join(', ')}`);
    }
  }

  /**
   * Force check for ended auctions (for testing/manual trigger)
   */
  async forceCheck(): Promise<any> {
    if (this.isRunning) {
      const error = new Error('Auction check is already running');
      console.warn('⚠️ Force check attempted while scheduler is already running');
      throw error;
    }

    this.isRunning = true;
    console.log('🔄 Force checking ended auctions...');

    try {
      const startTime = Date.now();
      const result = await this.checkEndedAuctions();
      const duration = Date.now() - startTime;

      console.log(`✅ Force check completed in ${duration}ms`);
      return result;
    } catch (error) {
      console.error('❌ Error during force check:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get auction scheduler status with enhanced monitoring
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval,
      nextCheck: this.getNextCheckTime(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      schedulerVersion: '2.0.0'
    };
  }

  /**
   * Get next scheduled check time with improved calculation
   */
  private getNextCheckTime(): string {
    const now = new Date();
    const nextCheck = new Date(now);

    // Calculate next 2-minute interval
    const currentMinutes = now.getMinutes();
    const nextMinutes = Math.ceil(currentMinutes / 2) * 2;

    if (nextMinutes >= 60) {
      nextCheck.setHours(nextCheck.getHours() + 1);
      nextCheck.setMinutes(0, 0, 0);
    } else {
      nextCheck.setMinutes(nextMinutes, 0, 0);
    }

    return nextCheck.toISOString();
  }

  /**
   * Get detailed statistics about auction processing
   */
  async getAuctionStats() {
    try {
      const [activeAuctions, completedAuctions, totalBids] = await Promise.all([
        prisma.product.count({
          where: {
            sellType: 'auction',
            auctionCompleted: false,
            isActive: true,
            status: 'active'
          }
        }),
        prisma.product.count({
          where: {
            sellType: 'auction',
            auctionCompleted: true
          }
        }),
        prisma.bid.count()
      ]);

      return {
        activeAuctions,
        completedAuctions,
        totalBids,
        lastUpdated: getCurrentISOString()
      };
    } catch (error) {
      console.error('❌ Error getting auction stats:', error);
      return {
        error: 'Failed to retrieve auction statistics',
        lastUpdated: getCurrentISOString()
      };
    }
  }
}

const auctionScheduler = new AuctionScheduler();
export default auctionScheduler;
