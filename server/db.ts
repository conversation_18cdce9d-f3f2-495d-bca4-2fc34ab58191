import { PrismaClient } from '../generated/client'
import { applyTimezoneHandling } from './utils/database.util'
import { databaseMonitor } from './utils/database-monitor.util'
import { performanceMonitor } from './utils/performance-monitor.util'

// Enhanced Prisma configuration for better performance
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development'
    ? [
        { emit: 'event', level: 'query' },
        { emit: 'stdout', level: 'error' },
        { emit: 'stdout', level: 'warn' },
      ]
    : [
        { emit: 'stdout', level: 'error' },
      ],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Connection pooling configuration
  __internal: {
    engine: {
      // Connection pool settings for better performance
      connectionLimit: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '10'),
      poolTimeout: parseInt(process.env.DATABASE_POOL_TIMEOUT || '10000'),
      transactionOptions: {
        maxWait: 5000, // 5 seconds
        timeout: 10000, // 10 seconds
      },
    },
  },
})

// Enhanced query logging for performance monitoring
if (process.env.NODE_ENV === 'development') {
  prisma.$on('query', (e) => {
    const duration = e.duration;
    const query = e.query;

    // Record query metrics for monitoring
    databaseMonitor.recordQuery(duration, query);
    performanceMonitor.recordDatabaseQuery(query, duration);

    // Log slow queries (> 100ms)
    if (duration > 100) {
      console.warn(`🐌 Slow Query (${duration}ms):`, {
        query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
        duration: `${duration}ms`,
        params: e.params,
      });
    }
  });

  // Start periodic database health reporting
  databaseMonitor.startPeriodicReporting(prisma, 15); // Every 15 minutes
}

// Apply timezone handling
applyTimezoneHandling(prisma)

// Graceful shutdown handling
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
