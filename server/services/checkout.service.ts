import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import shippingService from './shipping.service';

class CheckoutService {
  // Generate unique order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  // Create shipping address
  async createShippingAddress(userId: string, addressData: any) {
    try {
      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.create({
        data: {
          ...addressData,
          userId
        }
      });

      return successResponse("Shipping address created successfully", address);
    } catch (error) {
      console.error("Create shipping address service error:", error);
      return errorResponse("Failed to create shipping address");
    }
  }

  // Get user's shipping addresses
  async getShippingAddresses(userId: string) {
    try {
      const addresses = await prisma.shippingAddress.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return successResponse("Shipping addresses retrieved successfully", addresses);
    } catch (error) {
      console.error("Get shipping addresses service error:", error);
      return errorResponse("Failed to retrieve shipping addresses");
    }
  }

  // Update shipping address
  async updateShippingAddress(userId: string, addressId: string, addressData: any) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true, id: { not: addressId } },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.update({
        where: { id: addressId },
        data: addressData
      });

      return successResponse("Shipping address updated successfully", address);
    } catch (error) {
      console.error("Update shipping address service error:", error);
      return errorResponse("Failed to update shipping address");
    }
  }

  // Delete shipping address
  async deleteShippingAddress(userId: string, addressId: string) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      await prisma.shippingAddress.delete({
        where: { id: addressId }
      });

      return successResponse("Shipping address deleted successfully");
    } catch (error) {
      console.error("Delete shipping address service error:", error);
      return errorResponse("Failed to delete shipping address");
    }
  }

  // Create order from cart
  async createOrderFromCart(userId: string, orderData: any) {
    try {
      // Get user's cart
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return errorResponse("Cart is empty");
      }

      // Validate all products are available
      for (const item of cart.items) {
        if (item.product.status !== 'active') {
          return errorResponse(`Product "${item.product.itemName}" is no longer available`);
        }
        if (item.product.sellType !== 'buy-now') {
          return errorResponse(`Product "${item.product.itemName}" is only available through auction`);
        }
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate totals
      const subtotal = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Calculate dynamic shipping cost
      let shippingCost = 0;
      if (shippingAddressId) {
        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cart.items);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = 15.00;
        }
      } else {
        shippingCost = 15.00; // Default shipping cost
      }

      const tax = subtotal * 0.1; // 10% tax
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending',
          paymentStatus: 'pending',
          paymentMethod: orderData.paymentMethod,
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          items: {
            create: cart.items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              currency: orderData.currency || 'USD'
            }))
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Clear cart after successful order creation
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create order from cart service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Create buy now order (single product)
  async createBuyNowOrder(userId: string, orderData: any) {
    try {
      const { products: productIds, quantity, paymentMethod } = orderData;

      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
      });

      if (!products || products.length === 0) {
        return errorResponse("Products not found");
      }

      // For single buy now, just use the first product
      const product = products[0];

      if (!product) {
        return errorResponse("Product not found");
      }

      // Check if all products are active
      const inactiveProducts = products.filter(item => item.status !== 'active');
      if (inactiveProducts.length > 0) {
        return errorResponse("Some products are not available for purchase");
      }

      // Check if all products are buy-now type (unless this is a bidding order)
      if (!orderData.orderType || orderData.orderType !== 'bidding') {
        const auctionProducts = products.filter(item => item.sellType !== 'buy-now');
        if (auctionProducts.length > 0) {
          return errorResponse("Some products are only available through auction");
        }
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate subtotal - use winning bid amount for bidding orders
      let subtotal = 0;
      if (orderData.orderType === 'bidding' && orderData.winningBid) {
        // Winning bid is stored in USD, convert to order currency if needed
        const winningBidUSD = Number(orderData.winningBid);
        if (orderData.currency === 'IDR') {
          // Convert USD to IDR
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(winningBidUSD, 'USD', 'IDR');
        } else {
          subtotal = winningBidUSD;
        }
      } else {
        // Regular products - convert from USD to order currency if needed
        const productTotalUSD = products.reduce((total, item) => {
          return total + Number(item.priceUSD ?? 0)
        }, 0);

        if (orderData.currency === 'IDR') {
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(productTotalUSD, 'USD', 'IDR');
        } else {
          subtotal = productTotalUSD;
        }
      }

      // Calculate dynamic shipping cost for buy now orders
      let shippingCost = 0;
      if (shippingAddressId) {
        const cartItems = productIds.map((id: string) => ({
          product: products.find(p => p.id === id),
          quantity: quantity || 1
        }));

        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cartItems);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // 20 USD = ~310,000 IDR
        }
      } else {
        shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // Default shipping cost
      }

      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending_payment',
          paymentStatus: 'pending',
          paymentMethod: paymentMethod.eWalletType ?? paymentMethod.type,
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          items: {
            create: productIds.map((id: string) => {
              const product = products.find(p => p.id === id);
              let itemPrice = Number(product?.priceUSD ?? 0);

              // For bidding orders, use the converted subtotal as the item price
              if (orderData.orderType === 'bidding' && orderData.winningBid) {
                itemPrice = subtotal; // This is already converted to the correct currency
              } else if (orderData.currency === 'IDR') {
                // Convert regular product price to IDR if needed
                itemPrice = subtotal / productIds.length; // Distribute total among items
              }

              return {
                productId: id,
                quantity: quantity || 1,
                price: itemPrice,
                currency: orderData.currency || 'USD',
                // Add bidding information if applicable
                ...(orderData.orderType === 'bidding' && orderData.bidId && {
                  bidId: orderData.bidId
                })
              };
            })
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Get user data for payment
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        return errorResponse("User not found");
      }

      // Create payment record and Xendit payment
      const paymentService = (await import('./payment.service')).default;
      let paymentResult = null;

      console.log("Creating payment for order:", order.id, "with method:", orderData.paymentMethod.type);

      // Create payment based on payment method
      if (orderData.paymentMethod.type === 'xendit_invoice' || orderData.paymentMethod.type === 'invoice') {
        console.log("Creating Xendit invoice for order:", order.id);
        paymentResult = await paymentService.createInvoice({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          description: `Payment for Order ${order.orderNumber}`,
          customerEmail: user.email || '<EMAIL>',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'ewallet') {
        console.log("Creating eWallet payment for order:", order.id, "with type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createEWalletCharge({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          ewalletType: orderData.paymentMethod.ewalletType || 'OVO',
          customerPhone: user.phoneNumber || '************',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'virtual_account') {
        console.log("Creating virtual account payment for order:", order.id, "with bank code:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createVirtualAccount({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          bankCode: orderData.paymentMethod.ewalletType || 'BCA',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'retail_outlet') {
        console.log("Creating retail outlet payment for order:", order.id, "with outlet name:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createRetailOutlet({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          retailOutletName: orderData.paymentMethod.ewalletType || 'INDOMARET',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'qr_code') {
        console.log("Creating QR code payment for order:", order.id, "with QR code type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createQRCode({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          qrCodeType: orderData.paymentMethod.ewalletType || 'QRIS',
          customerName: `${user.firstName} ${user.lastName}`
        });
      }

      // Clear cart if this is a buy now order
      await prisma.cartItem.deleteMany({
        where: { cart: { userId } }
      });

      // Get updated order with payment
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true,
          payment: true
        }
      });

      const response = {
        ...updatedOrder,
        subtotal: Number(updatedOrder!.subtotal),
        shippingCost: Number(updatedOrder!.shippingCost),
        tax: Number(updatedOrder!.tax),
        total: Number(updatedOrder!.total),
        items: updatedOrder!.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: products.map(prod => ({
            ...prod,
            priceUSD: Number(prod.priceUSD),
            currentBid: prod.currentBid ? Number(prod.currentBid) : null,
          }))
        })),
        paymentUrl: (paymentResult?.data as any)?.invoiceUrl || (paymentResult?.data as any)?.actions?.mobile_deeplink_checkout_url
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create buy now order service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Get user's orders
  async getOrders(userId: string, query: any) {
    try {
      const { page, limit, status, paymentStatus, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { userId };
      if (status) where.status = status;
      if (paymentStatus) where.paymentStatus = paymentStatus;

      const orders = await prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit
      });

      const total = await prisma.order.count({ where });

      const response = {
        orders: orders.map(order => ({
          ...order,
          subtotal: Number(order.subtotal),
          shippingCost: Number(order.shippingCost),
          tax: Number(order.tax),
          total: Number(order.total),
          items: order.items.map(item => ({
            ...item,
            price: Number(item.price),
            product: {
              ...item.product,
              priceUSD: Number(item.product.priceUSD),
              currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            }
          }))
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      return successResponse("Orders retrieved successfully", response);
    } catch (error) {
      console.error("Get orders service error:", error);
      return errorResponse("Failed to retrieve orders");
    }
  }

  // Get single order
  async getOrder(userId: string, orderId: string) {
    try {
      const order = await prisma.order.findFirst({
        where: { id: orderId, userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order retrieved successfully", response);
    } catch (error) {
      console.error("Get order service error:", error);
      return errorResponse("Failed to retrieve order");
    }
  }

  // Update order status (admin function)
  async updateOrderStatus(orderId: string, statusData: any) {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: statusData,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order status updated successfully", response);
    } catch (error) {
      console.error("Update order status service error:", error);
      return errorResponse("Failed to update order status");
    }
  }
}

const checkoutService = new CheckoutService();
export default checkoutService;
