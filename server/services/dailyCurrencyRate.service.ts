import { PrismaClient } from "../../generated/client";
import { errorResponse, successResponse } from "../utils/response.util";
import crypto from "crypto";
import { getJakartaTime, getStartOfDayJakarta } from "../utils/timezone.util";
import { createTodayQuery, getCurrentISOString, formatDateForDB } from "../utils/database.util";

// Create a dedicated Prisma instance for currency service
const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

interface CurrencyRateData {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  source: string;
  margin?: number;
}

class DailyCurrencyRateService {
  private readonly DEFAULT_MARGIN = 0.02; // 2% margin
  private readonly SUPPORTED_PAIRS = [
    { from: 'USD', to: 'IDR' },
    { from: 'IDR', to: 'USD' }
  ];

  /**
   * Fetch and save daily currency rates
   */
  async updateDailyRates() {
    let isConnected = false;

    try {
      console.log('Starting daily currency rate update...');

      // Test database connection first
      await this.ensureDatabaseConnection();
      isConnected = true;

      // Get today's date query in Jakarta timezone
      const todayQuery = createTodayQuery();
      const today = getStartOfDayJakarta();

      // Optimized check for existing rates with selective fields
      const existingRates = await this.safeQuery(() =>
        prisma.currencyRate.findMany({
          where: {
            date: todayQuery,
            isActive: true
          },
          select: {
            id: true,
            fromCurrency: true,
            toCurrency: true,
            rate: true,
            updatedAt: true,
          }
        })
      );

      if (existingRates && existingRates.length > 0) {
        console.log('Rates for today already exist, updating...');
      }

      // Fetch rates from multiple sources
      const rateResults = await Promise.allSettled([
        this.fetchFromExchangeRateAPI(),
        this.fetchFromFixerAPI(),
        this.fetchFromCurrencyAPI()
      ]);

      let bestRate = null;
      let bestSource = '';

      // Find the best rate (most reliable source)
      for (const result of rateResults) {
        if (result.status === 'fulfilled' && result.value) {
          bestRate = result.value.rate;
          bestSource = result.value.source;
          break;
        }
      }

      if (!bestRate) {
        console.error('Failed to fetch rates from all sources');
        return errorResponse("Failed to fetch currency rates");
      }

      // Calculate rates with margin
      const usdToIdrRate = bestRate;
      const idrToUsdRate = 1 / bestRate;

      // Save USD to IDR rate
      const usdToIdrSaved = await this.saveCurrencyRate({
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        rate: usdToIdrRate,
        source: bestSource,
        margin: this.DEFAULT_MARGIN
      }, today);

      // Save IDR to USD rate
      const idrToUsdSaved = await this.saveCurrencyRate({
        fromCurrency: 'IDR',
        toCurrency: 'USD',
        rate: idrToUsdRate,
        source: bestSource,
        margin: this.DEFAULT_MARGIN
      }, today);

      if (!usdToIdrSaved || !idrToUsdSaved) {
        console.error('Failed to save some currency rates to database');
        return errorResponse("Failed to save currency rates to database");
      }

      console.log(`Daily rates updated successfully. USD/IDR: ${usdToIdrRate}, Source: ${bestSource}`);

      return successResponse("Daily currency rates updated successfully", {
        usdToIdr: usdToIdrRate,
        idrToUsd: idrToUsdRate,
        source: bestSource,
        date: formatDateForDB(today)
      });

    } catch (error) {
      console.error('Update daily rates error:', error);

      // If database connection failed, return a more specific error
      if (!isConnected) {
        return errorResponse("Database connection failed - currency rates not updated");
      }

      return errorResponse("Failed to update daily currency rates");
    } finally {
      // Ensure we don't leave hanging connections
      try {
        await prisma.$disconnect();
      } catch (disconnectError) {
        console.error('Error disconnecting from database:', disconnectError);
      }
    }
  }

  /**
   * Ensure database connection is working
   */
  private async ensureDatabaseConnection(): Promise<void> {
    try {
      await prisma.$connect();
      // Test with a simple query
      await prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection established');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw new Error('Database connection failed');
    }
  }

  /**
   * Safe query execution with retry logic
   */
  private async safeQuery<T>(queryFn: () => Promise<T>, retries: number = 3): Promise<T | null> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await queryFn();
      } catch (error) {
        console.error(`Query attempt ${attempt} failed:`, error);

        if (attempt === retries) {
          console.error('All query attempts failed');
          return null;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
    return null;
  }

  /**
   * Safe create or update currency rate using raw SQL with ON DUPLICATE KEY UPDATE
   */
  private async safeCreateOrUpdateCurrencyRate(data: {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
    sellRate: number;
    buyRate: number;
    margin: number;
    source: string;
    date: Date;
  }): Promise<boolean> {
    try {
      // Use raw SQL with ON DUPLICATE KEY UPDATE to handle unique constraint
      const dateStr = formatDateForDB(data.date).split('T')[0]; // Format as YYYY-MM-DD
      const now = getCurrentISOString().replace('T', ' ').replace('Z', ''); // Format for MySQL
      const uuid = crypto.randomUUID();

      await prisma.$executeRaw`
        INSERT INTO CurrencyRate (
          id, fromCurrency, toCurrency, rate, sellRate, buyRate,
          margin, source, date, isActive, createdAt, updatedAt
        ) VALUES (
          ${uuid}, ${data.fromCurrency}, ${data.toCurrency}, ${data.rate},
          ${data.sellRate}, ${data.buyRate}, ${data.margin}, ${data.source},
          ${dateStr}, true, ${now}, ${now}
        )
        ON DUPLICATE KEY UPDATE
          rate = VALUES(rate),
          sellRate = VALUES(sellRate),
          buyRate = VALUES(buyRate),
          margin = VALUES(margin),
          source = VALUES(source),
          isActive = VALUES(isActive),
          updatedAt = VALUES(updatedAt)
      `;

      return true;
    } catch (error: any) {
      console.error('Failed to create/update currency rate with raw SQL:', error);
      return false;
    }
  }

  /**
   * Save currency rate to database
   */
  private async saveCurrencyRate(data: CurrencyRateData, date: Date): Promise<boolean> {
    const { fromCurrency, toCurrency, rate, source, margin = this.DEFAULT_MARGIN } = data;

    // Calculate sell and buy rates with margin
    const sellRate = rate * (1 + margin); // Add margin for selling
    const buyRate = rate * (1 - margin);  // Subtract margin for buying

    try {
      return await this.safeCreateOrUpdateCurrencyRate({
        fromCurrency,
        toCurrency,
        rate,
        sellRate,
        buyRate,
        margin,
        source,
        date
      });
    } catch (error) {
      console.error('Error saving currency rate:', error);
      return false;
    }
  }

  /**
   * Get current active rates
   */
  async getCurrentRates() {
    try {
      // Get today's date in Jakarta timezone
      const today = getStartOfDayJakarta();

      const rates = await prisma.currencyRate.findMany({
        where: {
          date: formatDateForDB(today),
          isActive: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      if (rates.length === 0) {
        // If no rates for today, get the latest rates
        const latestRates = await prisma.currencyRate.findMany({
          where: {
            isActive: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 2
        });

        return successResponse("Latest currency rates retrieved", {
          rates: latestRates,
          isToday: false
        });
      }

      return successResponse("Current currency rates retrieved", {
        rates,
        isToday: true
      });

    } catch (error) {
      console.error('Get current rates error:', error);
      return errorResponse("Failed to get current rates");
    }
  }

  /**
   * Convert currency using database rates with API fallback
   */
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string, type: 'buy' | 'sell' = 'sell') {
    try {
      // Validate input
      if (!amount || isNaN(amount) || amount <= 0) {
        return errorResponse("Invalid amount provided");
      }

      if (!fromCurrency || !toCurrency) {
        return errorResponse("Currency codes are required");
      }

      if (fromCurrency === toCurrency) {
        return successResponse("Currency conversion completed", {
          originalAmount: amount,
          convertedAmount: amount,
          rate: 1,
          type,
          source: 'same_currency'
        });
      }

      // Try to get rate from database first
      const dbResult = await this.getConversionFromDatabase(amount, fromCurrency, toCurrency, type);
      if ('success' in dbResult && dbResult.success) {
        return dbResult;
      }

      console.log('No database rate found, fetching from API and saving...');

      // If no database rate, fetch from API and save
      const apiResult = await this.fetchAndSaveRate(fromCurrency, toCurrency);
      if (!apiResult.success) {
        // Last resort - use fallback static rates
        return this.getFallbackConversion(amount, fromCurrency, toCurrency, type);
      }

      // Now try conversion again with newly saved rate
      const retryResult = await this.getConversionFromDatabase(amount, fromCurrency, toCurrency, type);
      if ('success' in retryResult && retryResult.success) {
        return retryResult;
      }

      // If still fails, use fallback
      return this.getFallbackConversion(amount, fromCurrency, toCurrency, type);

    } catch (error) {
      console.error('Convert currency error:', error);
      // Use fallback conversion on any error
      return this.getFallbackConversion(amount, fromCurrency, toCurrency, type);
    }
  }

  /**
   * Get conversion from database
   */
  private async getConversionFromDatabase(amount: number, fromCurrency: string, toCurrency: string, type: 'buy' | 'sell') {
    try {
      const today = getStartOfDayJakarta();

      // Try today's rate first
      let currencyRate = await this.safeQuery(() =>
        prisma.currencyRate.findFirst({
          where: {
            fromCurrency,
            toCurrency,
            date: formatDateForDB(today),
            isActive: true
          }
        })
      );

      // If no rate for today, get latest rate
      if (!currencyRate) {
        currencyRate = await this.safeQuery(() =>
          prisma.currencyRate.findFirst({
            where: {
              fromCurrency,
              toCurrency,
              isActive: true
            },
            orderBy: {
              date: 'desc'
            }
          })
        );
      }

      if (!currencyRate) {
        return { success: false, message: "No rate found in database" };
      }

      const rate = type === 'sell' ? Number(currencyRate.sellRate) : Number(currencyRate.buyRate);

      // Validate rate
      if (!rate || isNaN(rate) || rate <= 0) {
        console.error('Invalid rate from database:', rate);
        return { success: false, message: "Invalid rate in database" };
      }

      const convertedAmount = amount * rate;

      // Validate converted amount
      if (isNaN(convertedAmount)) {
        console.error('Conversion resulted in NaN:', { amount, rate, convertedAmount });
        return { success: false, message: "Conversion calculation failed" };
      }

      return successResponse("Currency conversion completed", {
        originalAmount: amount,
        convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
        rate: Math.round(rate * 10000) / 10000, // Round to 4 decimal places
        type,
        source: 'database',
        rateDate: currencyRate.date
      });

    } catch (error) {
      console.error('Database conversion error:', error);
      return { success: false, message: "Database query failed" };
    }
  }

  /**
   * Fetch rate from API and save to database
   */
  private async fetchAndSaveRate(fromCurrency: string, toCurrency: string) {
    try {
      console.log(`Fetching rate for ${fromCurrency} to ${toCurrency} from API...`);

      // Only support USD-IDR and IDR-USD for now
      if (!((fromCurrency === 'USD' && toCurrency === 'IDR') || (fromCurrency === 'IDR' && toCurrency === 'USD'))) {
        return { success: false, message: "Currency pair not supported" };
      }

      // Fetch USD to IDR rate
      const usdToIdrRate = await this.fetchLiveUsdToIdrRate();
      if (!usdToIdrRate) {
        return { success: false, message: "Failed to fetch live rate" };
      }

      const today = getStartOfDayJakarta();

      // Save both USD->IDR and IDR->USD rates
      const usdToIdrSaved = await this.saveCurrencyRate({
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        rate: usdToIdrRate,
        source: 'api_fallback',
        margin: this.DEFAULT_MARGIN
      }, today);

      const idrToUsdSaved = await this.saveCurrencyRate({
        fromCurrency: 'IDR',
        toCurrency: 'USD',
        rate: 1 / usdToIdrRate,
        source: 'api_fallback',
        margin: this.DEFAULT_MARGIN
      }, today);

      if (usdToIdrSaved && idrToUsdSaved) {
        console.log(`✅ Successfully saved rates: USD/IDR = ${usdToIdrRate}`);
        return { success: true, rate: usdToIdrRate };
      }

      return { success: false, message: "Failed to save rates to database" };

    } catch (error) {
      console.error('Fetch and save rate error:', error);
      return { success: false, message: "API fetch failed" };
    }
  }

  /**
   * Fetch live USD to IDR rate from external API
   */
  private async fetchLiveUsdToIdrRate(): Promise<number | null> {
    try {
      // Try ExchangeRate-API first
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');

      if (response.ok) {
        const data = await response.json();
        if (data.rates && data.rates.IDR && !isNaN(data.rates.IDR)) {
          return data.rates.IDR;
        }
      }

      // Fallback to other APIs if needed
      console.log('ExchangeRate-API failed, trying other sources...');

      // Try Fixer.io if API key available
      const fixerApiKey = process.env.FIXER_API_KEY;
      if (fixerApiKey) {
        const fixerResponse = await fetch(`https://api.fixer.io/latest?access_key=${fixerApiKey}&base=USD&symbols=IDR`);
        if (fixerResponse.ok) {
          const fixerData = await fixerResponse.json();
          if (fixerData.success && fixerData.rates && fixerData.rates.IDR && !isNaN(fixerData.rates.IDR)) {
            return fixerData.rates.IDR;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Fetch live rate error:', error);
      return null;
    }
  }

  /**
   * Get fallback conversion using static rates
   */
  private getFallbackConversion(amount: number, fromCurrency: string, toCurrency: string, type: 'buy' | 'sell') {
    const FALLBACK_RATES = {
      USD_TO_IDR: 15000,
      IDR_TO_USD: 1 / 15000,
    };

    let rate = 1;

    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      rate = type === 'sell'
        ? FALLBACK_RATES.USD_TO_IDR * (1 + this.DEFAULT_MARGIN)
        : FALLBACK_RATES.USD_TO_IDR * (1 - this.DEFAULT_MARGIN);
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      rate = type === 'sell'
        ? FALLBACK_RATES.IDR_TO_USD * (1 + this.DEFAULT_MARGIN)
        : FALLBACK_RATES.IDR_TO_USD * (1 - this.DEFAULT_MARGIN);
    } else {
      return errorResponse("Currency pair not supported in fallback mode");
    }

    // Validate rate
    if (!rate || isNaN(rate) || rate <= 0) {
      return errorResponse("Invalid fallback rate calculated");
    }

    const convertedAmount = amount * rate;

    // Validate converted amount
    if (isNaN(convertedAmount)) {
      return errorResponse("Fallback conversion calculation failed");
    }

    return successResponse("Currency conversion completed (fallback)", {
      originalAmount: amount,
      convertedAmount: Math.round(convertedAmount * 100) / 100,
      rate: Math.round(rate * 10000) / 10000,
      type,
      source: 'fallback',
      rateDate: getCurrentISOString()
    });
  }

  /**
   * Create manual currency rate
   */
  async createManualRate(data: {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
    sellRate: number;
    buyRate: number;
    margin: number;
    source: string;
    date: Date;
  }) {
    try {
      const success = await this.safeCreateOrUpdateCurrencyRate(data);

      if (success) {
        return successResponse("Manual currency rate created/updated successfully");
      } else {
        return errorResponse("Failed to create manual currency rate");
      }

    } catch (error) {
      console.error('Create manual rate error:', error);
      return errorResponse("Failed to create manual currency rate");
    }
  }

  /**
   * Fetch rates from ExchangeRate-API
   */
  private async fetchFromExchangeRateAPI() {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (!response.ok) {
        throw new Error(`ExchangeRate-API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.rates && data.rates.IDR) {
        return {
          rate: data.rates.IDR,
          source: 'exchangerate-api.com'
        };
      }

      return null;
    } catch (error) {
      console.error('ExchangeRate-API failed:', error);
      return null;
    }
  }

  /**
   * Fetch rates from Fixer.io
   */
  private async fetchFromFixerAPI() {
    try {
      const apiKey = process.env.FIXER_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(`https://api.fixer.io/latest?access_key=${apiKey}&base=USD&symbols=IDR`);
      
      if (!response.ok) {
        throw new Error(`Fixer API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.rates && data.rates.IDR) {
        return {
          rate: data.rates.IDR,
          source: 'fixer.io'
        };
      }

      return null;
    } catch (error) {
      console.error('Fixer API failed:', error);
      return null;
    }
  }

  /**
   * Fetch rates from CurrencyAPI
   */
  private async fetchFromCurrencyAPI() {
    try {
      const apiKey = process.env.CURRENCY_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(`https://api.currencyapi.com/v3/latest?apikey=${apiKey}&base_currency=USD&currencies=IDR`);
      
      if (!response.ok) {
        throw new Error(`CurrencyAPI error: ${response.status}`);
      }

      const data = await response.json();

      if (data.data && data.data.IDR && data.data.IDR.value) {
        return {
          rate: data.data.IDR.value,
          source: 'currencyapi.com'
        };
      }

      return null;
    } catch (error) {
      console.error('CurrencyAPI failed:', error);
      return null;
    }
  }

  /**
   * Get rate history
   */
  async getRateHistory(fromCurrency: string, toCurrency: string, days: number = 30) {
    try {
      const endDate = getStartOfDayJakarta();
      const startDate = getStartOfDayJakarta();
      startDate.setDate(startDate.getDate() - days);

      const rates = await prisma.currencyRate.findMany({
        where: {
          fromCurrency,
          toCurrency,
          date: {
            gte: formatDateForDB(startDate),
            lte: formatDateForDB(endDate)
          },
          isActive: true
        },
        orderBy: {
          date: 'asc'
        }
      });

      return successResponse("Rate history retrieved successfully", {
        rates,
        period: {
          from: startDate,
          to: endDate,
          days
        }
      });

    } catch (error) {
      console.error('Get rate history error:', error);
      return errorResponse("Failed to get rate history");
    }
  }
}

const dailyCurrencyRateService = new DailyCurrencyRateService();
export default dailyCurrencyRateService;
