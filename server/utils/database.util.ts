import { PrismaClient } from '../../generated/client';
import { getJakartaTime, toJakartaTime, jakartaToUTC, utcToJakarta } from './timezone.util';

/**
 * Database utility functions for handling timezone conversions
 */

/**
 * Convert date to ISO string in Jakarta timezone for database storage
 */
export const toISOStringForDB = (date: Date | string = new Date()): string => {
  return toJakartaTime(date).toISOString();
};

/**
 * Convert date to UTC before saving to database
 */
export const toUTCForDB = (date: Date | string): Date => {
  return jakartaToUTC(date);
};

/**
 * Convert date from UTC to Jakarta timezone after reading from database
 */
export const fromUTCFromDB = (date: Date | string): Date => {
  return utcToJakarta(date);
};

/**
 * Get current timestamp as ISO string in Jakarta timezone
 */
export const getCurrentISOString = (): string => {
  return getJakartaTime().toISOString();
};

/**
 * Create Prisma client with timezone configuration
 */
export const createPrismaClient = (): PrismaClient => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
};

/**
 * Apply timezone handling to Prisma client
 * Note: This function applies date conversion logic
 */
export const applyTimezoneHandling = (prisma: PrismaClient) => {
  // We'll handle timezone conversion manually in services
  // since Prisma middleware is deprecated
  console.log('✅ Timezone handling configured for Jakarta timezone');
  return prisma;
};

/**
 * Convert date fields to ISO string in Jakarta timezone
 */
export const convertDatesToISOString = (obj: any): void => {
  if (!obj || typeof obj !== 'object') return;

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // Convert date fields to ISO string
      if (isDateField(key)) {
        if (value instanceof Date) {
          obj[key] = toJakartaTime(value).toISOString();
        } else if (typeof value === 'string' && isDateString(value)) {
          obj[key] = toJakartaTime(value).toISOString();
        } else if (!value && (key === 'createdAt' || key === 'updatedAt')) {
          obj[key] = getCurrentISOString();
        }
      } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // Recursively convert nested objects
        convertDatesToISOString(value);
      }
    }
  }
};

/**
 * Check if a field name is a date field
 */
function isDateField(fieldName: string): boolean {
  const dateFields = [
    'createdAt', 'updatedAt', 'deletedAt',
    'auctionStartDate', 'auctionEndDate',
    'paymentDeadline', 'estimatedDelivery',
    'shippedAt', 'deliveredAt',
    'date', 'timestamp', 'time',
    'lastBidTime', 'bidTime',
    'paidAt', 'expiredAt'
  ];

  return dateFields.includes(fieldName) ||
         fieldName.endsWith('At') ||
         fieldName.endsWith('Date') ||
         fieldName.endsWith('Time');
}

/**
 * Check if a string is a valid date string
 */
function isDateString(str: string): boolean {
  // Check for ISO date format
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
  return isoDateRegex.test(str) && !isNaN(Date.parse(str));
}

/**
 * Get current timestamp in Jakarta timezone for database operations
 */
export const getCurrentTimestampForDB = (): string => {
  return getCurrentISOString();
};

/**
 * Create date range query for Jakarta timezone
 */
export const createDateRangeQuery = (
  startDate: Date | string,
  endDate: Date | string
) => {
  return {
    gte: toJakartaTime(startDate).toISOString(),
    lte: toJakartaTime(endDate).toISOString(),
  };
};

/**
 * Create date query for today in Jakarta timezone
 */
export const createTodayQuery = () => {
  const jakartaTime = getJakartaTime();
  const startOfDay = new Date(jakartaTime);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(jakartaTime);
  endOfDay.setHours(23, 59, 59, 999);

  return {
    gte: startOfDay.toISOString(),
    lte: endOfDay.toISOString(),
  };
};

/**
 * Format date for database storage (ISO string)
 */
export const formatDateForDB = (date: Date | string = new Date()): string => {
  return toJakartaTime(date).toISOString();
};

/**
 * Parse date from database (ISO string) to Jakarta timezone
 */
export const parseDateFromDB = (isoString: string): Date => {
  return toJakartaTime(isoString);
};
