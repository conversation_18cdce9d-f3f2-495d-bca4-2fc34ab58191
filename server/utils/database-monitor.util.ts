import { PrismaClient } from '../../generated/client';

interface QueryMetrics {
  totalQueries: number;
  slowQueries: number;
  averageQueryTime: number;
  longestQueryTime: number;
  queryTimeDistribution: {
    fast: number; // < 50ms
    medium: number; // 50-200ms
    slow: number; // 200-500ms
    verySlow: number; // > 500ms
  };
}

interface ConnectionMetrics {
  activeConnections: number;
  maxConnections: number;
  connectionPoolUtilization: number;
}

class DatabaseMonitor {
  private queryMetrics: QueryMetrics = {
    totalQueries: 0,
    slowQueries: 0,
    averageQueryTime: 0,
    longestQueryTime: 0,
    queryTimeDistribution: {
      fast: 0,
      medium: 0,
      slow: 0,
      verySlow: 0,
    },
  };

  private queryTimes: number[] = [];
  private readonly maxQueryHistorySize = 1000;

  /**
   * Record query execution time
   */
  recordQuery(duration: number, query: string) {
    this.queryMetrics.totalQueries++;
    this.queryTimes.push(duration);

    // Keep only recent queries for memory efficiency
    if (this.queryTimes.length > this.maxQueryHistorySize) {
      this.queryTimes.shift();
    }

    // Update longest query time
    if (duration > this.queryMetrics.longestQueryTime) {
      this.queryMetrics.longestQueryTime = duration;
    }

    // Count slow queries
    if (duration > 200) {
      this.queryMetrics.slowQueries++;
    }

    // Update query time distribution
    if (duration < 50) {
      this.queryMetrics.queryTimeDistribution.fast++;
    } else if (duration < 200) {
      this.queryMetrics.queryTimeDistribution.medium++;
    } else if (duration < 500) {
      this.queryMetrics.queryTimeDistribution.slow++;
    } else {
      this.queryMetrics.queryTimeDistribution.verySlow++;
    }

    // Calculate average query time
    this.queryMetrics.averageQueryTime = 
      this.queryTimes.reduce((sum, time) => sum + time, 0) / this.queryTimes.length;

    // Log very slow queries with details
    if (duration > 500) {
      console.error(`🚨 Very Slow Query Detected (${duration}ms):`, {
        query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get current query metrics
   */
  getQueryMetrics(): QueryMetrics {
    return { ...this.queryMetrics };
  }

  /**
   * Get database connection metrics
   */
  async getConnectionMetrics(prisma: PrismaClient): Promise<ConnectionMetrics> {
    try {
      // Get MySQL connection status
      const result = await prisma.$queryRaw`SHOW STATUS LIKE 'Threads_connected'` as any[];
      const activeConnections = result[0]?.Value ? parseInt(result[0].Value) : 0;

      const maxResult = await prisma.$queryRaw`SHOW VARIABLES LIKE 'max_connections'` as any[];
      const maxConnections = maxResult[0]?.Value ? parseInt(maxResult[0].Value) : 0;

      const utilization = maxConnections > 0 ? (activeConnections / maxConnections) * 100 : 0;

      return {
        activeConnections,
        maxConnections,
        connectionPoolUtilization: Math.round(utilization * 100) / 100,
      };
    } catch (error) {
      console.error('Failed to get connection metrics:', error);
      return {
        activeConnections: 0,
        maxConnections: 0,
        connectionPoolUtilization: 0,
      };
    }
  }

  /**
   * Get comprehensive database health report
   */
  async getDatabaseHealthReport(prisma: PrismaClient) {
    const queryMetrics = this.getQueryMetrics();
    const connectionMetrics = await this.getConnectionMetrics(prisma);

    const healthScore = this.calculateHealthScore(queryMetrics, connectionMetrics);

    return {
      timestamp: new Date().toISOString(),
      healthScore,
      queryMetrics,
      connectionMetrics,
      recommendations: this.generateRecommendations(queryMetrics, connectionMetrics),
    };
  }

  /**
   * Calculate database health score (0-100)
   */
  private calculateHealthScore(queryMetrics: QueryMetrics, connectionMetrics: ConnectionMetrics): number {
    let score = 100;

    // Penalize for slow queries
    const slowQueryRatio = queryMetrics.slowQueries / Math.max(queryMetrics.totalQueries, 1);
    score -= slowQueryRatio * 30;

    // Penalize for high average query time
    if (queryMetrics.averageQueryTime > 100) {
      score -= Math.min((queryMetrics.averageQueryTime - 100) / 10, 20);
    }

    // Penalize for high connection pool utilization
    if (connectionMetrics.connectionPoolUtilization > 80) {
      score -= (connectionMetrics.connectionPoolUtilization - 80) / 2;
    }

    return Math.max(Math.round(score), 0);
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(queryMetrics: QueryMetrics, connectionMetrics: ConnectionMetrics): string[] {
    const recommendations: string[] = [];

    const slowQueryRatio = queryMetrics.slowQueries / Math.max(queryMetrics.totalQueries, 1);
    
    if (slowQueryRatio > 0.1) {
      recommendations.push('High number of slow queries detected. Consider adding database indexes or optimizing query patterns.');
    }

    if (queryMetrics.averageQueryTime > 150) {
      recommendations.push('Average query time is high. Review and optimize frequently used queries.');
    }

    if (connectionMetrics.connectionPoolUtilization > 80) {
      recommendations.push('Connection pool utilization is high. Consider increasing connection pool size or optimizing connection usage.');
    }

    if (queryMetrics.queryTimeDistribution.verySlow > 0) {
      recommendations.push('Very slow queries detected. Immediate optimization required for queries taking >500ms.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Database performance is healthy. Continue monitoring.');
    }

    return recommendations;
  }

  /**
   * Reset metrics (useful for testing or periodic resets)
   */
  resetMetrics() {
    this.queryMetrics = {
      totalQueries: 0,
      slowQueries: 0,
      averageQueryTime: 0,
      longestQueryTime: 0,
      queryTimeDistribution: {
        fast: 0,
        medium: 0,
        slow: 0,
        verySlow: 0,
      },
    };
    this.queryTimes = [];
  }

  /**
   * Start periodic health reporting
   */
  startPeriodicReporting(prisma: PrismaClient, intervalMinutes: number = 15) {
    setInterval(async () => {
      try {
        const report = await this.getDatabaseHealthReport(prisma);
        console.log('📊 Database Health Report:', {
          healthScore: report.healthScore,
          totalQueries: report.queryMetrics.totalQueries,
          averageQueryTime: `${report.queryMetrics.averageQueryTime.toFixed(2)}ms`,
          slowQueries: report.queryMetrics.slowQueries,
          connectionUtilization: `${report.connectionMetrics.connectionPoolUtilization}%`,
          recommendations: report.recommendations,
        });

        // Alert if health score is low
        if (report.healthScore < 70) {
          console.warn('⚠️ Database health score is low:', report.healthScore);
        }
      } catch (error) {
        console.error('Failed to generate database health report:', error);
      }
    }, intervalMinutes * 60 * 1000);
  }
}

export const databaseMonitor = new DatabaseMonitor();
export default databaseMonitor;
