import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/id'; // Indonesian locale

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

// Set default locale to Indonesian
dayjs.locale('id');

// Jakarta timezone utilities for backend
export const JAKARTA_TIMEZONE = 'Asia/Jakarta';

/**
 * Get current time in Jakarta timezone
 */
export const getJakartaTime = (): Date => {
  return dayjs().tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Convert any date to Jakarta timezone
 */
export const toJakartaTime = (date: Date | string): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Get start of day in Jakarta timezone
 */
export const getStartOfDayJakarta = (date: Date | string = new Date()): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).startOf('day').toDate();
};

/**
 * Get end of day in Jakarta timezone
 */
export const getEndOfDayJakarta = (date: Date | string = new Date()): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).endOf('day').toDate();
};

/**
 * Check if a date is in the past (Jakarta timezone)
 */
export const isDatePastJakarta = (date: Date | string): boolean => {
  const inputDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const now = dayjs().tz(JAKARTA_TIMEZONE);
  return inputDate.isBefore(now);
};

/**
 * Check if a date is in the future (Jakarta timezone)
 */
export const isDateFutureJakarta = (date: Date | string): boolean => {
  const inputDate = dayjs(date).tz(JAKARTA_TIMEZONE);
  const now = dayjs().tz(JAKARTA_TIMEZONE);
  return inputDate.isAfter(now);
};

/**
 * Convert Jakarta time to UTC for database storage
 */
export const jakartaToUTC = (date: Date | string): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).utc().toDate();
};

/**
 * Convert UTC to Jakarta time for display
 */
export const utcToJakarta = (date: Date | string): Date => {
  return dayjs.utc(date).tz(JAKARTA_TIMEZONE).toDate();
};

/**
 * Format date for Jakarta timezone
 */
export const formatJakartaDate = (
  date: Date | string,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).format(format);
};

/**
 * Format date for Jakarta timezone with Indonesian locale
 */
export const formatJakartaDateID = (
  date: Date | string,
  format: string = 'DD MMMM YYYY HH:mm'
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).locale('id').format(format);
};

/**
 * Get timezone offset for Jakarta (always +7)
 */
export const getJakartaOffset = (): number => {
  return dayjs().tz(JAKARTA_TIMEZONE).utcOffset() / 60; // Convert minutes to hours
};

/**
 * Create a date in Jakarta timezone from components
 */
export const createJakartaDate = (
  year: number,
  month: number,
  day: number,
  hour: number = 0,
  minute: number = 0,
  second: number = 0
): Date => {
  return dayjs.tz(`${year}-${month}-${day} ${hour}:${minute}:${second}`, JAKARTA_TIMEZONE).toDate();
};

/**
 * Add time to a date in Jakarta timezone
 */
export const addTimeJakarta = (
  date: Date | string,
  amount: number,
  unit: dayjs.ManipulateType = 'millisecond'
): Date => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).add(amount, unit).toDate();
};

/**
 * Get time difference in Jakarta timezone (in milliseconds)
 */
export const getTimeDifferenceJakarta = (
  startDate: Date | string,
  endDate: Date | string = new Date()
): number => {
  const start = dayjs(startDate).tz(JAKARTA_TIMEZONE);
  const end = dayjs(endDate).tz(JAKARTA_TIMEZONE);
  return end.diff(start);
};

/**
 * Format relative time from now in Jakarta timezone
 */
export const fromNowJakarta = (
  date: Date | string,
  withoutSuffix: boolean = false
): string => {
  return dayjs(date).tz(JAKARTA_TIMEZONE).fromNow(withoutSuffix);
};
