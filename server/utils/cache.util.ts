interface CacheItem {
  value: any;
  expiry: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheConfig {
  defaultTTL: number; // seconds
  maxSize: number; // maximum number of items
  cleanupInterval: number; // cleanup interval in ms
}

class CacheManager {
  private cache = new Map<string, CacheItem>();
  private config: CacheConfig = {
    defaultTTL: 300, // 5 minutes default
    maxSize: 1000, // maximum 1000 items
    cleanupInterval: 60000, // cleanup every minute
  };
  private cleanupTimer: NodeJS.Timeout | null = null;
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
  };

  /**
   * Initialize in-memory cache
   */
  async initialize() {
    try {
      console.log('✅ In-memory cache initialized successfully');

      // Start periodic cleanup
      this.startCleanup();

      // Log cache stats periodically
      setInterval(() => {
        this.logStats();
      }, 5 * 60 * 1000); // Every 5 minutes

    } catch (error) {
      console.error('Failed to initialize cache:', error);
    }
  }

  /**
   * Start periodic cleanup of expired items
   */
  private startCleanup() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Clean up expired items and enforce size limits
   */
  private cleanup() {
    const now = Date.now();
    let expiredCount = 0;

    // Remove expired items
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    // Enforce size limit using LRU eviction
    if (this.cache.size > this.config.maxSize) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

      const itemsToRemove = this.cache.size - this.config.maxSize;
      for (let i = 0; i < itemsToRemove; i++) {
        this.cache.delete(sortedEntries[i][0]);
        this.stats.evictions++;
      }
    }

    if (expiredCount > 0) {
      console.log(`🧹 Cache cleanup: removed ${expiredCount} expired items, current size: ${this.cache.size}`);
    }
  }

  /**
   * Set cache value
   */
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    try {
      const expiration = ttl || this.config.defaultTTL;
      const expiryTime = Date.now() + (expiration * 1000);

      const cacheItem: CacheItem = {
        value: value,
        expiry: expiryTime,
        accessCount: 0,
        lastAccessed: Date.now(),
      };

      this.cache.set(key, cacheItem);
      this.stats.sets++;

      // Trigger cleanup if cache is getting full
      if (this.cache.size > this.config.maxSize * 0.9) {
        this.cleanup();
      }

      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Get cache value
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const item = this.cache.get(key);

      if (!item) {
        this.stats.misses++;
        return null;
      }

      // Check if expired
      if (Date.now() > item.expiry) {
        this.cache.delete(key);
        this.stats.misses++;
        return null;
      }

      // Update access statistics
      item.accessCount++;
      item.lastAccessed = Date.now();
      this.stats.hits++;

      return item.value as T;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Delete cache value
   */
  async delete(key: string): Promise<boolean> {
    try {
      const deleted = this.cache.delete(key);
      if (deleted) {
        this.stats.deletes++;
      }
      return deleted;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple cache keys by pattern
   */
  async deletePattern(pattern: string): Promise<boolean> {
    try {
      let deletedCount = 0;
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));

      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
          deletedCount++;
        }
      }

      this.stats.deletes += deletedCount;
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  /**
   * Check if cache is available
   */
  isAvailable(): boolean {
    return true; // In-memory cache is always available
  }

  /**
   * Get or set cache with fallback function
   */
  async getOrSet<T>(
    key: string,
    fallbackFn: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, execute fallback function
    const result = await fallbackFn();

    // Store in cache for next time
    await this.set(key, result, ttl);

    return result;
  }

  /**
   * Increment counter in cache
   */
  async increment(key: string, amount: number = 1): Promise<number | null> {
    try {
      const current = await this.get<number>(key) || 0;
      const newValue = current + amount;
      await this.set(key, newValue);
      return newValue;
    } catch (error) {
      console.error('Cache increment error:', error);
      return null;
    }
  }

  /**
   * Set expiration for existing key
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const item = this.cache.get(key);
      if (item) {
        item.expiry = Date.now() + (ttl * 1000);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Cache expire error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    const now = Date.now();
    let expiredCount = 0;
    let totalSize = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        expiredCount++;
      }
      totalSize += JSON.stringify(item.value).length;
    }

    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : '0.00';

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      expiredCount,
      totalSizeBytes: totalSize,
      hitRate: `${hitRate}%`,
      stats: { ...this.stats },
    };
  }

  /**
   * Log cache statistics
   */
  private logStats() {
    this.getStats().then(stats => {
      if (stats) {
        console.log('📊 Cache Stats:', {
          size: `${stats.size}/${stats.maxSize}`,
          hitRate: stats.hitRate,
          totalSizeKB: Math.round(stats.totalSizeBytes / 1024),
          expired: stats.expiredCount,
        });
      }
    });
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<boolean> {
    try {
      this.cache.clear();
      this.stats = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        evictions: 0,
      };
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Check if key exists
   */
  async has(key: string): Promise<boolean> {
    const item = this.cache.get(key);
    if (!item) return false;

    // Check if expired
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Disconnect (cleanup for in-memory cache)
   */
  async disconnect() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.cache.clear();
  }
}

// Cache key generators for different data types
export const CacheKeys = {
  // Product caching
  product: (id: string) => `product:${id}`,
  productBySlug: (slug: string) => `product:slug:${slug}`,
  productList: (params: string) => `products:list:${params}`,
  productWithPricing: (currency: string, params: string) => `products:pricing:${currency}:${params}`,
  
  // Currency rates
  currencyRate: (from: string, to: string, date: string) => `currency:${from}:${to}:${date}`,
  currentRates: () => `currency:current`,
  
  // User data
  userCart: (userId: string) => `cart:${userId}`,
  userOrders: (userId: string, params: string) => `orders:${userId}:${params}`,
  
  // Categories and types
  categories: () => `categories:all`,
  itemTypes: (categoryId?: string) => categoryId ? `itemtypes:${categoryId}` : `itemtypes:all`,
  
  // Bidding
  bidHistory: (productId: string) => `bids:${productId}`,
  userBids: (userId: string, params: string) => `bids:user:${userId}:${params}`,
  
  // Statistics
  dashboardStats: () => `stats:dashboard`,
  productStats: (productId: string) => `stats:product:${productId}`,
};

// TTL constants (in seconds)
export const CacheTTL = {
  SHORT: 60,        // 1 minute
  MEDIUM: 300,      // 5 minutes
  LONG: 900,        // 15 minutes
  VERY_LONG: 3600,  // 1 hour
  DAILY: 86400,     // 24 hours
};

// Create singleton instance
export const cacheManager = new CacheManager();

// Initialize cache on module load
if (process.env.NODE_ENV !== 'test') {
  cacheManager.initialize().catch(console.error);
}

export default cacheManager;
