import { Decimal } from '@prisma/client/runtime/library';

/**
 * Optimized response utilities for better performance and smaller payload sizes
 */

interface OptimizationOptions {
  removeNulls?: boolean;
  convertDecimals?: boolean;
  limitArrays?: number;
  excludeFields?: string[];
  includeFields?: string[];
}

class ResponseOptimizer {
  /**
   * Optimize product data for API responses
   */
  optimizeProduct(product: any, options: OptimizationOptions = {}): any {
    const {
      removeNulls = true,
      convertDecimals = true,
      limitArrays,
      excludeFields = [],
      includeFields = []
    } = options;

    if (!product) return null;

    let optimized = { ...product };

    // Convert Decimal fields to numbers
    if (convertDecimals) {
      if (optimized.priceUSD) optimized.priceUSD = Number(optimized.priceUSD);
      if (optimized.currentBid) optimized.currentBid = Number(optimized.currentBid);
    }

    // Optimize images array
    if (optimized.images && Array.isArray(optimized.images)) {
      optimized.images = optimized.images
        .slice(0, limitArrays || optimized.images.length)
        .map((img: any) => this.optimizeImage(img, { removeNulls, excludeFields }));
    }

    // Optimize bids array
    if (optimized.bids && Array.isArray(optimized.bids)) {
      optimized.bids = optimized.bids
        .slice(0, limitArrays || optimized.bids.length)
        .map((bid: any) => this.optimizeBid(bid, { removeNulls, convertDecimals }));
    }

    // Optimize seller data
    if (optimized.seller) {
      optimized.seller = this.optimizeUser(optimized.seller, { removeNulls, excludeFields });
    }

    // Optimize category and itemType
    if (optimized.category) {
      optimized.category = this.optimizeCategory(optimized.category, { removeNulls });
    }

    if (optimized.itemType) {
      optimized.itemType = this.optimizeItemType(optimized.itemType, { removeNulls });
    }

    // Remove excluded fields
    excludeFields.forEach(field => {
      delete optimized[field];
    });

    // Keep only included fields if specified
    if (includeFields.length > 0) {
      const filtered: any = {};
      includeFields.forEach(field => {
        if (optimized[field] !== undefined) {
          filtered[field] = optimized[field];
        }
      });
      optimized = filtered;
    }

    // Remove null values if requested
    if (removeNulls) {
      optimized = this.removeNullValues(optimized);
    }

    return optimized;
  }

  /**
   * Optimize product list for listing pages
   */
  optimizeProductList(products: any[], options: OptimizationOptions = {}): any[] {
    const listOptions = {
      ...options,
      limitArrays: options.limitArrays || 3, // Limit images for listing
      excludeFields: [
        'description', // Remove description from listing
        'extendedBiddingEnabled',
        'extendedBiddingMinutes',
        'extendedBiddingDuration',
        'auctionCompletedAt',
        ...(options.excludeFields || [])
      ]
    };

    return products.map(product => this.optimizeProduct(product, listOptions));
  }

  /**
   * Optimize image data
   */
  private optimizeImage(image: any, options: OptimizationOptions = {}): any {
    if (!image) return null;

    let optimized = {
      id: image.id,
      imageUrl: image.imageUrl,
      altText: image.altText,
      isMain: image.isMain,
    };

    // Include sortOrder only if needed
    // if (image.sortOrder !== undefined && image.sortOrder !== 0) {
    //   optimized = { ...optimized, sortOrder: image.sortOrder };
    // }

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Optimize bid data
   */
  private optimizeBid(bid: any, options: OptimizationOptions = {}): any {
    if (!bid) return null;

    let optimized = {
      id: bid.id,
      amount: options.convertDecimals ? Number(bid.amount) : bid.amount,
      bidType: bid.bidType,
      createdAt: bid.createdAt,
    };

    // Include bidder info if available
    if (bid.bidder) {
      optimized = {
        ...optimized,
        bidder: this.optimizeUser(bid.bidder, options)
      };
    }

    // Include winning status if relevant
    if (bid.isWinning !== undefined) {
      optimized = { ...optimized, isWinning: bid.isWinning };
    }

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Optimize user data
   */
  private optimizeUser(user: any, options: OptimizationOptions = {}): any {
    if (!user) return null;

    let optimized = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
    };

    // Include email only if not in excluded fields
    if (user.email && !options.excludeFields?.includes('email')) {
      optimized = { ...optimized, email: user.email };
    }

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Optimize category data
   */
  private optimizeCategory(category: any, options: OptimizationOptions = {}): any {
    if (!category) return null;

    const optimized = {
      id: category.id,
      name: category.name,
      sellType: category.sellType,
    };

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Optimize item type data
   */
  private optimizeItemType(itemType: any, options: OptimizationOptions = {}): any {
    if (!itemType) return null;

    const optimized = {
      id: itemType.id,
      name: itemType.name,
    };

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Optimize order data
   */
  optimizeOrder(order: any, options: OptimizationOptions = {}): any {
    if (!order) return null;

    let optimized = { ...order };

    // Convert decimal fields
    if (options.convertDecimals) {
      if (optimized.subtotal) optimized.subtotal = Number(optimized.subtotal);
      if (optimized.shippingCost) optimized.shippingCost = Number(optimized.shippingCost);
      if (optimized.tax) optimized.tax = Number(optimized.tax);
      if (optimized.total) optimized.total = Number(optimized.total);
    }

    // Optimize order items
    if (optimized.items && Array.isArray(optimized.items)) {
      optimized.items = optimized.items.map((item: any) => ({
        ...item,
        price: options.convertDecimals ? Number(item.price) : item.price,
        product: item.product ? this.optimizeProduct(item.product, {
          ...options,
          limitArrays: 1, // Only main image for order items
          excludeFields: ['description', 'bids', ...(options.excludeFields || [])]
        }) : null,
        bid: item.bid ? this.optimizeBid(item.bid, options) : null,
      }));
    }

    return options.removeNulls ? this.removeNullValues(optimized) : optimized;
  }

  /**
   * Remove null and undefined values from object
   */
  private removeNullValues(obj: any): any {
    if (obj === null || obj === undefined) return obj;
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.removeNullValues(item)).filter(item => item !== null && item !== undefined);
    }
    
    if (typeof obj === 'object') {
      const cleaned: any = {};
      Object.keys(obj).forEach(key => {
        const value = this.removeNullValues(obj[key]);
        if (value !== null && value !== undefined) {
          cleaned[key] = value;
        }
      });
      return cleaned;
    }
    
    return obj;
  }

  /**
   * Calculate response size in bytes
   */
  calculateResponseSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  /**
   * Optimize pagination response
   */
  optimizePaginationResponse(data: any, options: OptimizationOptions = {}) {
    const optimized = {
      ...data,
      products: data.products ? this.optimizeProductList(data.products, options) : undefined,
      orders: data.orders ? data.orders.map((order: any) => this.optimizeOrder(order, options)) : undefined,
    };

    // Calculate response size for monitoring
    const responseSize = this.calculateResponseSize(optimized);
    
    // Log large responses
    if (responseSize > 100000) { // 100KB
      console.warn(`📦 Large API Response (${Math.round(responseSize / 1024)}KB):`, {
        itemCount: data.products?.length || data.orders?.length || 0,
        sizeKB: Math.round(responseSize / 1024),
      });
    }

    return optimized;
  }
}

export const responseOptimizer = new ResponseOptimizer();
export default responseOptimizer;
