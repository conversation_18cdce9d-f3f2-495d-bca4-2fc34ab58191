interface PerformanceMetrics {
  apiRequests: {
    total: number;
    slow: number; // > 1000ms
    verySlow: number; // > 3000ms
    averageResponseTime: number;
    endpoints: Map<string, {
      count: number;
      totalTime: number;
      slowCount: number;
      maxTime: number;
    }>;
  };
  database: {
    totalQueries: number;
    slowQueries: number; // > 200ms
    verySlow: number; // > 500ms
    averageQueryTime: number;
    queryTypes: Map<string, {
      count: number;
      totalTime: number;
      slowCount: number;
    }>;
  };
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cache: {
    hitRate: number;
    size: number;
    maxSize: number;
  };
}

interface Alert {
  type: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  metric: string;
  value: number;
  threshold: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    apiRequests: {
      total: 0,
      slow: 0,
      verySlow: 0,
      averageResponseTime: 0,
      endpoints: new Map(),
    },
    database: {
      totalQueries: 0,
      slowQueries: 0,
      verySlow: 0,
      averageQueryTime: 0,
      queryTypes: new Map(),
    },
    memory: {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0,
    },
    cache: {
      hitRate: 0,
      size: 0,
      maxSize: 0,
    },
  };

  private alerts: Alert[] = [];
  private readonly maxAlerts = 100;
  private responseTimes: number[] = [];
  private queryTimes: number[] = [];
  private readonly maxHistorySize = 1000;

  // Thresholds for alerting
  private thresholds = {
    slowApiResponse: 1000, // ms
    verySlowApiResponse: 3000, // ms
    slowQuery: 200, // ms
    verySlowQuery: 500, // ms
    highMemoryUsage: 500 * 1024 * 1024, // 500MB
    lowCacheHitRate: 70, // %
    highErrorRate: 5, // %
  };

  /**
   * Record API request performance
   */
  recordApiRequest(endpoint: string, method: string, responseTime: number, statusCode: number) {
    const key = `${method} ${endpoint}`;
    
    // Update overall metrics
    this.metrics.apiRequests.total++;
    this.responseTimes.push(responseTime);
    
    // Keep only recent response times
    if (this.responseTimes.length > this.maxHistorySize) {
      this.responseTimes.shift();
    }
    
    // Calculate average
    this.metrics.apiRequests.averageResponseTime = 
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
    
    // Count slow requests
    if (responseTime > this.thresholds.slowApiResponse) {
      this.metrics.apiRequests.slow++;
      
      if (responseTime > this.thresholds.verySlowApiResponse) {
        this.metrics.apiRequests.verySlow++;
        this.addAlert('warning', `Very slow API response: ${key}`, 'api_response_time', responseTime, this.thresholds.verySlowApiResponse);
      }
    }
    
    // Update endpoint-specific metrics
    const endpointStats = this.metrics.apiRequests.endpoints.get(key) || {
      count: 0,
      totalTime: 0,
      slowCount: 0,
      maxTime: 0,
    };
    
    endpointStats.count++;
    endpointStats.totalTime += responseTime;
    endpointStats.maxTime = Math.max(endpointStats.maxTime, responseTime);
    
    if (responseTime > this.thresholds.slowApiResponse) {
      endpointStats.slowCount++;
    }
    
    this.metrics.apiRequests.endpoints.set(key, endpointStats);
  }

  /**
   * Record database query performance
   */
  recordDatabaseQuery(query: string, duration: number) {
    // Extract query type (SELECT, INSERT, UPDATE, DELETE)
    const queryType = query.trim().split(' ')[0].toUpperCase();
    
    // Update overall metrics
    this.metrics.database.totalQueries++;
    this.queryTimes.push(duration);
    
    // Keep only recent query times
    if (this.queryTimes.length > this.maxHistorySize) {
      this.queryTimes.shift();
    }
    
    // Calculate average
    this.metrics.database.averageQueryTime = 
      this.queryTimes.reduce((sum, time) => sum + time, 0) / this.queryTimes.length;
    
    // Count slow queries
    if (duration > this.thresholds.slowQuery) {
      this.metrics.database.slowQueries++;
      
      if (duration > this.thresholds.verySlowQuery) {
        this.metrics.database.verySlow++;
        this.addAlert('warning', `Very slow database query: ${queryType}`, 'query_time', duration, this.thresholds.verySlowQuery);
      }
    }
    
    // Update query type metrics
    const typeStats = this.metrics.database.queryTypes.get(queryType) || {
      count: 0,
      totalTime: 0,
      slowCount: 0,
    };
    
    typeStats.count++;
    typeStats.totalTime += duration;
    
    if (duration > this.thresholds.slowQuery) {
      typeStats.slowCount++;
    }
    
    this.metrics.database.queryTypes.set(queryType, typeStats);
  }

  /**
   * Update memory metrics
   */
  updateMemoryMetrics() {
    const memUsage = process.memoryUsage();
    this.metrics.memory = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
    };
    
    // Check for high memory usage
    if (memUsage.heapUsed > this.thresholds.highMemoryUsage) {
      this.addAlert('warning', 'High memory usage detected', 'memory_usage', memUsage.heapUsed, this.thresholds.highMemoryUsage);
    }
  }

  /**
   * Update cache metrics
   */
  updateCacheMetrics(hitRate: number, size: number, maxSize: number) {
    this.metrics.cache = { hitRate, size, maxSize };
    
    // Check for low cache hit rate
    if (hitRate < this.thresholds.lowCacheHitRate) {
      this.addAlert('warning', 'Low cache hit rate', 'cache_hit_rate', hitRate, this.thresholds.lowCacheHitRate);
    }
  }

  /**
   * Add alert
   */
  private addAlert(type: Alert['type'], message: string, metric: string, value: number, threshold: number) {
    const alert: Alert = {
      type,
      message,
      timestamp: new Date(),
      metric,
      value,
      threshold,
    };
    
    this.alerts.unshift(alert);
    
    // Keep only recent alerts
    if (this.alerts.length > this.maxAlerts) {
      this.alerts.pop();
    }
    
    // Log critical alerts immediately
    if (type === 'critical') {
      console.error(`🚨 CRITICAL ALERT: ${message}`, { metric, value, threshold });
    } else if (type === 'error') {
      console.error(`❌ ERROR: ${message}`, { metric, value, threshold });
    } else {
      console.warn(`⚠️ WARNING: ${message}`, { metric, value, threshold });
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryMetrics();
    return JSON.parse(JSON.stringify(this.metrics));
  }

  /**
   * Get recent alerts
   */
  getAlerts(limit: number = 20): Alert[] {
    return this.alerts.slice(0, limit);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const metrics = this.getMetrics();
    
    return {
      timestamp: new Date().toISOString(),
      api: {
        totalRequests: metrics.apiRequests.total,
        averageResponseTime: Math.round(metrics.apiRequests.averageResponseTime),
        slowRequestsPercentage: metrics.apiRequests.total > 0 
          ? Math.round((metrics.apiRequests.slow / metrics.apiRequests.total) * 100)
          : 0,
      },
      database: {
        totalQueries: metrics.database.totalQueries,
        averageQueryTime: Math.round(metrics.database.averageQueryTime),
        slowQueriesPercentage: metrics.database.totalQueries > 0
          ? Math.round((metrics.database.slowQueries / metrics.database.totalQueries) * 100)
          : 0,
      },
      memory: {
        heapUsedMB: Math.round(metrics.memory.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(metrics.memory.heapTotal / 1024 / 1024),
        rssMB: Math.round(metrics.memory.rss / 1024 / 1024),
      },
      cache: {
        hitRate: Math.round(metrics.cache.hitRate),
        utilizationPercentage: metrics.cache.maxSize > 0
          ? Math.round((metrics.cache.size / metrics.cache.maxSize) * 100)
          : 0,
      },
      alerts: this.alerts.length,
    };
  }

  /**
   * Start periodic monitoring
   */
  startMonitoring(intervalMinutes: number = 5) {
    setInterval(() => {
      const summary = this.getPerformanceSummary();
      console.log('📊 Performance Summary:', summary);
      
      // Check for performance issues
      if (summary.api.slowRequestsPercentage > 10) {
        this.addAlert('warning', `High percentage of slow API requests: ${summary.api.slowRequestsPercentage}%`, 'slow_api_percentage', summary.api.slowRequestsPercentage, 10);
      }
      
      if (summary.database.slowQueriesPercentage > 15) {
        this.addAlert('warning', `High percentage of slow database queries: ${summary.database.slowQueriesPercentage}%`, 'slow_query_percentage', summary.database.slowQueriesPercentage, 15);
      }
      
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Reset metrics (useful for testing)
   */
  resetMetrics() {
    this.metrics = {
      apiRequests: {
        total: 0,
        slow: 0,
        verySlow: 0,
        averageResponseTime: 0,
        endpoints: new Map(),
      },
      database: {
        totalQueries: 0,
        slowQueries: 0,
        verySlow: 0,
        averageQueryTime: 0,
        queryTypes: new Map(),
      },
      memory: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        rss: 0,
      },
      cache: {
        hitRate: 0,
        size: 0,
        maxSize: 0,
      },
    };
    this.alerts = [];
    this.responseTimes = [];
    this.queryTimes = [];
  }
}

export const performanceMonitor = new PerformanceMonitor();
export default performanceMonitor;
