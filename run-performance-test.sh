#!/bin/bash

# Performance Testing Script for King Collectibles API
# This script runs comprehensive performance tests and generates reports

echo "🚀 King Collectibles API Performance Testing"
echo "============================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if axios is available
if ! node -e "require('axios')" 2>/dev/null; then
    echo "📦 Installing axios for performance testing..."
    npm install axios
fi

# Set default values
API_URL=${API_URL:-"http://localhost:3001/api/v1"}
CONCURRENT_USERS=${CONCURRENT_USERS:-10}
TEST_DURATION=${TEST_DURATION:-60}
RAMP_UP_TIME=${RAMP_UP_TIME:-10}

echo "Configuration:"
echo "  API URL: $API_URL"
echo "  Concurrent Users: $CONCURRENT_USERS"
echo "  Test Duration: ${TEST_DURATION}s"
echo "  Ramp-up Time: ${RAMP_UP_TIME}s"
echo ""

# Check if API is running
echo "🔍 Checking API availability..."
if curl -s "$API_URL/health" > /dev/null; then
    echo "✅ API is running and accessible"
else
    echo "❌ API is not accessible at $API_URL"
    echo "Please make sure the API server is running"
    exit 1
fi

# Create results directory
RESULTS_DIR="performance-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULT_FILE="$RESULTS_DIR/performance_test_$TIMESTAMP.log"

mkdir -p "$RESULTS_DIR"

echo "📊 Starting performance test..."
echo "Results will be saved to: $RESULT_FILE"
echo ""

# Run the performance test
API_URL="$API_URL" \
CONCURRENT_USERS="$CONCURRENT_USERS" \
TEST_DURATION="$TEST_DURATION" \
RAMP_UP_TIME="$RAMP_UP_TIME" \
node test-performance.js 2>&1 | tee "$RESULT_FILE"

# Check test results
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo ""
    echo "✅ Performance test completed successfully!"
    echo "📄 Results saved to: $RESULT_FILE"
    
    # Extract key metrics from results
    echo ""
    echo "📈 Quick Summary:"
    echo "=================="
    
    if grep -q "PERFORMANCE TEST RESULTS" "$RESULT_FILE"; then
        echo "✅ Test completed with results"
        
        # Extract key metrics
        TOTAL_REQUESTS=$(grep "Total Requests:" "$RESULT_FILE" | awk '{print $3}')
        SUCCESS_RATE=$(grep "Successful:" "$RESULT_FILE" | grep -o '([0-9.]*%)' | tr -d '()')
        AVG_RESPONSE=$(grep "Average:" "$RESULT_FILE" | awk '{print $2}')
        P95_RESPONSE=$(grep "95th percentile:" "$RESULT_FILE" | awk '{print $3}')
        
        echo "  Total Requests: $TOTAL_REQUESTS"
        echo "  Success Rate: $SUCCESS_RATE"
        echo "  Average Response Time: ${AVG_RESPONSE}ms"
        echo "  95th Percentile: ${P95_RESPONSE}ms"
        
        # Performance assessment
        if grep -q "Performance looks good!" "$RESULT_FILE"; then
            echo "  Assessment: ✅ Performance is good!"
        else
            echo "  Assessment: ⚠️ Performance issues detected"
            echo ""
            echo "🔧 Recommendations from test:"
            grep "^•" "$RESULT_FILE" | head -5
        fi
    else
        echo "⚠️ Test may have encountered issues"
    fi
    
    echo ""
    echo "📊 Additional Analysis:"
    echo "======================"
    
    # Check for specific performance indicators
    if grep -q "Slow response:" "$RESULT_FILE"; then
        SLOW_COUNT=$(grep -c "Slow response:" "$RESULT_FILE")
        echo "  ⚠️ Detected $SLOW_COUNT slow responses (>2s)"
    else
        echo "  ✅ No slow responses detected"
    fi
    
    if grep -q "Request failed:" "$RESULT_FILE"; then
        FAILED_COUNT=$(grep -c "Request failed:" "$RESULT_FILE")
        echo "  ❌ Detected $FAILED_COUNT failed requests"
    else
        echo "  ✅ No failed requests detected"
    fi
    
    echo ""
    echo "🎯 Next Steps:"
    echo "=============="
    echo "1. Review the detailed results in $RESULT_FILE"
    echo "2. Check API monitoring at $API_URL/metrics"
    echo "3. Monitor system health at $API_URL/health"
    
    if grep -q "Performance issues detected" "$RESULT_FILE" 2>/dev/null; then
        echo "4. ⚠️ Address performance issues identified in the test"
        echo "5. Consider running the test again after optimizations"
    else
        echo "4. ✅ Performance looks good! Consider stress testing with higher load"
    fi
    
else
    echo ""
    echo "❌ Performance test failed!"
    echo "📄 Check the log file for details: $RESULT_FILE"
    exit 1
fi

echo ""
echo "🏁 Performance testing completed!"
echo "📁 All results saved in: $RESULTS_DIR/"

# List all result files
echo ""
echo "📋 Available test results:"
ls -la "$RESULTS_DIR/"
