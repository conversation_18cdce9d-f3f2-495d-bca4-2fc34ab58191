# 🚀 Deployment Guide

This guide covers the optimized deployment process for King Collectibles application with smart migration and seeding.

## 📋 Quick Reference

### Development
```bash
npm run dev:full          # Start development with WebSocket
npm run db:reset          # Reset database with sample data
```

### Staging Deployment
```bash
npm run deploy:staging    # Full staging deployment
```

### Production Deployment
```bash
npm run deploy:production # Full production deployment
```

### Emergency
```bash
npm run emergency:restore # Emergency data restoration
npm run rollback:dry-run  # Check what rollback would do
```

## 🔧 Deployment Scripts

### Pre-Deployment Check
```bash
npm run deploy:check      # Check deployment readiness
```
- Validates environment configuration
- Checks database connection
- Verifies migration status
- Confirms required environment variables

### Smart Database Management
```bash
npm run db:deploy         # Smart deployment (staging)
npm run db:deploy:production # Production-safe deployment
```

**Smart Deployment Process:**
1. Generate Prisma client
2. Deploy pending migrations
3. Smart seeding (checks existing data)
4. Verification

### Seeding Options
```bash
npm run db:seed:smart     # Intelligent seeding (recommended)
npm run db:seed:production # Production-safe seeding only
npm run db:seed           # Full seeding with sample data
npm run db:seed:check     # Check what needs seeding
```

## 🌍 Environment-Specific Behavior

### Development
- **Seeding**: Full sample data including users and products
- **Migrations**: Development mode with reset capability
- **Safety**: Low (data can be reset)

### Staging
- **Seeding**: Smart seeding (essential data + existing data check)
- **Migrations**: Deploy mode (no reset)
- **Safety**: Medium (preserves existing data)

### Production
- **Seeding**: Essential system data only (no sample data)
- **Migrations**: Deploy mode with verification
- **Safety**: High (maximum data protection)

## 📊 Database Status Monitoring

### Check Status
```bash
npm run db:status         # Full database status
npm run db:seed:check     # Seeding status only
npm run db:migrate:status # Migration status only
```

### Maintenance
```bash
npm run maintenance:clean # Clean old data and optimize
```

## 🚨 Rollback & Recovery

### Safe Rollback
```bash
npm run rollback:dry-run  # See what would be rolled back
npm run rollback          # Rollback recent data changes
```

### Emergency Recovery
```bash
npm run emergency:restore # Full emergency restoration
npm run rollback:migrations # Rollback migrations (dangerous)
```

## 🔒 Safety Features

### Conflict Prevention
- **Smart Seeding**: Checks existing data before seeding
- **Upsert Operations**: Updates existing data safely
- **Environment Detection**: Different behavior per environment

### Data Protection
- **Automatic Backups**: Created before rollback operations
- **Dry Run Mode**: Preview changes before applying
- **Health Checks**: Verify system after operations

### Migration Safety
- **Status Verification**: Check migration state before deployment
- **Rollback Capability**: Emergency migration rollback
- **Conflict Detection**: Identify migration conflicts

## 📈 Deployment Workflow

### 1. Pre-Deployment
```bash
# Check readiness
npm run deploy:check

# Review what will be deployed
npm run db:migrate:status
npm run db:seed:check
```

### 2. Staging Deployment
```bash
# Full staging deployment
npm run deploy:staging
```

This runs:
1. `predeploy` - Deployment readiness check
2. `db:deploy` - Smart database deployment
3. `build` - Application build
4. `start` - Start application

### 3. Production Deployment
```bash
# Production deployment
npm run deploy:production
```

This runs:
1. `predeploy` - Deployment readiness check
2. `db:deploy:production` - Production-safe database deployment
3. `build:production` - Production build

### 4. Post-Deployment
```bash
# Verify deployment
npm run postdeploy
```

## 🔧 Environment Variables

### Required for All Environments
```env
DATABASE_URL=mysql://...
NEXTAUTH_SECRET=your-secret
NEXTAUTH_URL=https://your-domain.com
```

### Required for Authentication
```env
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### Optional for Enhanced Features
```env
EXCHANGE_RATE_API_KEY=your-api-key
FIXER_API_KEY=your-fixer-key
WISE_API_KEY=your-wise-key
```

## 🚨 Troubleshooting

### Migration Conflicts
```bash
# Check current status
npm run db:migrate:status

# If conflicts exist
npm run db:migrate:reset  # DEV ONLY
# Then re-run migrations
```

### Seeding Issues
```bash
# Check what's missing
npm run db:seed:check

# Force reseed (DEV ONLY)
npm run db:seed:force

# Production-safe reseed
npm run db:seed:production
```

### Deployment Failures
```bash
# Check what went wrong
npm run deploy:check

# Rollback if needed
npm run rollback:dry-run
npm run rollback

# Emergency restore
npm run emergency:restore
```

### Database Issues
```bash
# Clean up old data
npm run maintenance:clean

# Fix currency duplicates
npm run clean-currency-duplicates

# Full health check
npm run db:status
```

## 📋 Exit Codes

Scripts use standard exit codes for CI/CD integration:
- `0`: Success
- `1`: Error (deployment should fail)
- `2`: Warning (deployment can continue with caution)

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Check Deployment Readiness
  run: npm run deploy:check

- name: Deploy to Production
  run: npm run deploy:production
  
- name: Verify Deployment
  run: npm run postdeploy
```

### Error Handling
```yaml
- name: Rollback on Failure
  if: failure()
  run: npm run rollback
```

## 📚 Best Practices

### For Development
1. Use `npm run db:reset` for fresh start
2. Use `npm run dev:full` for complete development setup
3. Regular `npm run maintenance:clean` to prevent bloat

### For Staging
1. Always run `npm run deploy:check` first
2. Use `npm run deploy:staging` for full deployment
3. Monitor with `npm run db:status`

### For Production
1. **Never** use development seeding commands
2. Always use `npm run deploy:production`
3. Schedule `npm run maintenance:clean` weekly
4. Keep backups of production data externally

### For Emergency
1. Use `npm run rollback:dry-run` to preview changes
2. Have external backups ready
3. Use `npm run emergency:restore` as last resort

## 🎯 Summary

The optimized deployment system provides:
- **Smart Seeding**: Prevents data conflicts
- **Environment Safety**: Different behavior per environment  
- **Conflict Prevention**: Checks before making changes
- **Easy Rollback**: Quick recovery from issues
- **Health Monitoring**: Continuous status checking
- **Automated Backups**: Safety net for operations

This ensures reliable, conflict-free deployments across all environments! 🚀
