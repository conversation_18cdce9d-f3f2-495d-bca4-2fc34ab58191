# Timezone Usage Examples

## Frontend (React/Next.js)

### Basic Usage

```typescript
import { 
  getJakartaTime, 
  formatJakartaDate, 
  formatDistanceToNowJakarta,
  isDatePastJakarta 
} from '@/utils/timezone';

// Get current time in Jakarta timezone
const now = getJakartaTime();
console.log('Current Jakarta time:', now);

// Format date in Jakarta timezone
const formatted = formatJakartaDate(new Date(), 'DD MMMM YYYY HH:mm');
console.log('Formatted:', formatted); // "20 Juli 2025 18:43"

// Check if auction has ended
const auctionEndDate = "2025-07-20T18:43:00.000Z";
const hasEnded = isDatePastJakarta(auctionEndDate);

// Show relative time
const timeAgo = formatDistanceToNowJakarta(auctionEndDate);
console.log('Time ago:', timeAgo); // "2 jam yang lalu"
```

### Component Usage

```typescript
// In a React component
const ProductCard = ({ product }) => {
  const timeLeft = product.auctionEndDate 
    ? !isDatePastJakarta(product.auctionEndDate)
      ? formatDistanceToNowJakarta(product.auctionEndDate)
      : 'Ended'
    : null;

  return (
    <div>
      <h3>{product.name}</h3>
      {timeLeft && <p>Time left: {timeLeft}</p>}
      <p>Created: {formatJakartaDate(product.createdAt, 'DD MMM YYYY')}</p>
    </div>
  );
};
```

## Backend (Node.js/Prisma)

### Service Usage

```typescript
import { 
  getCurrentISOString, 
  formatDateForDB, 
  convertDatesToISOString 
} from '../utils/database.util';
import { getJakartaTime } from '../utils/timezone.util';

// Create new record with Jakarta timezone
const createProduct = async (data) => {
  const productData = {
    ...data,
    createdAt: getCurrentISOString(),
    updatedAt: getCurrentISOString(),
    auctionEndDate: formatDateForDB(data.auctionEndDate)
  };

  // Convert all date fields to ISO string
  convertDatesToISOString(productData);

  return await prisma.product.create({
    data: productData
  });
};

// Query with date range
const getProductsToday = async () => {
  const today = getStartOfDayJakarta();
  const tomorrow = getEndOfDayJakarta();

  return await prisma.product.findMany({
    where: {
      createdAt: {
        gte: formatDateForDB(today),
        lte: formatDateForDB(tomorrow)
      }
    }
  });
};
```

### Scheduler Usage

```typescript
import cron from 'node-cron';
import { getCurrentISOString } from '../utils/database.util';

// Schedule job with Jakarta timezone
cron.schedule('0 6 * * *', async () => {
  console.log('Running daily job at 6 AM Jakarta time');
  
  // Update records with current timestamp
  await prisma.someModel.updateMany({
    where: { status: 'pending' },
    data: { 
      processedAt: getCurrentISOString(),
      updatedAt: getCurrentISOString()
    }
  });
}, {
  timezone: "Asia/Jakarta"
});
```

## Database Storage Format

All dates are stored as ISO strings in Jakarta timezone:

```
createdAt: "2025-07-20T18:43:00.000Z"
updatedAt: "2025-07-20T18:43:00.000Z"
auctionEndDate: "2025-07-21T15:00:00.000Z"
```

## Key Benefits

1. **Consistent Timezone**: All dates use Jakarta timezone (Asia/Jakarta)
2. **ISO String Format**: Standardized format for database storage
3. **Day.js Integration**: More accurate timezone handling than native Date
4. **Automatic Conversion**: Utilities handle conversion automatically
5. **Indonesian Locale**: Date formatting supports Indonesian language

## Migration Notes

- All existing `new Date()` calls should use `getCurrentISOString()`
- Date queries should use `formatDateForDB()` for proper formatting
- Frontend components should use Jakarta timezone utilities
- Schedulers should specify `timezone: "Asia/Jakarta"`
