const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_URL || 'http://localhost:3001/api/v1';
const CONCURRENT_USERS = parseInt(process.env.CONCURRENT_USERS) || 10;
const TEST_DURATION = parseInt(process.env.TEST_DURATION) || 60; // seconds
const RAMP_UP_TIME = parseInt(process.env.RAMP_UP_TIME) || 10; // seconds

class PerformanceTest {
  constructor() {
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: [],
      startTime: null,
      endTime: null,
    };
    this.isRunning = false;
  }

  /**
   * Test endpoints with different scenarios
   */
  async runLoadTest() {
    console.log('🚀 Starting Performance Load Test');
    console.log(`Configuration:
  - Base URL: ${BASE_URL}
  - Concurrent Users: ${CONCURRENT_USERS}
  - Test Duration: ${TEST_DURATION}s
  - Ramp-up Time: ${RAMP_UP_TIME}s
`);

    this.results.startTime = Date.now();
    this.isRunning = true;

    // Test scenarios
    const scenarios = [
      { name: 'Get Products', endpoint: '/products', weight: 40 },
      { name: 'Get Product by ID', endpoint: '/products/test-id', weight: 20 },
      { name: 'Get Categories', endpoint: '/master/categories', weight: 15 },
      { name: 'Get Currency Rates', endpoint: '/currency-rates/current', weight: 10 },
      { name: 'Health Check', endpoint: '/health', weight: 10 },
      { name: 'Metrics', endpoint: '/metrics', weight: 5 },
    ];

    // Create user sessions
    const userPromises = [];
    for (let i = 0; i < CONCURRENT_USERS; i++) {
      const delay = (i / CONCURRENT_USERS) * RAMP_UP_TIME * 1000;
      userPromises.push(this.simulateUser(scenarios, delay));
    }

    // Stop test after duration
    setTimeout(() => {
      this.isRunning = false;
      console.log('\n⏰ Test duration reached, stopping...');
    }, TEST_DURATION * 1000);

    // Wait for all users to complete
    await Promise.allSettled(userPromises);

    this.results.endTime = Date.now();
    this.generateReport();
  }

  /**
   * Simulate a single user session
   */
  async simulateUser(scenarios, initialDelay = 0) {
    // Initial delay for ramp-up
    if (initialDelay > 0) {
      await this.sleep(initialDelay);
    }

    while (this.isRunning) {
      try {
        // Select random scenario based on weight
        const scenario = this.selectScenario(scenarios);
        await this.makeRequest(scenario);
        
        // Random think time between requests (100ms - 2s)
        const thinkTime = Math.random() * 1900 + 100;
        await this.sleep(thinkTime);
      } catch (error) {
        // Continue even if individual request fails
      }
    }
  }

  /**
   * Select scenario based on weight
   */
  selectScenario(scenarios) {
    const totalWeight = scenarios.reduce((sum, s) => sum + s.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const scenario of scenarios) {
      random -= scenario.weight;
      if (random <= 0) {
        return scenario;
      }
    }
    
    return scenarios[0]; // fallback
  }

  /**
   * Make HTTP request and record metrics
   */
  async makeRequest(scenario) {
    const startTime = Date.now();
    
    try {
      this.results.totalRequests++;
      
      const response = await axios.get(`${BASE_URL}${scenario.endpoint}`, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Performance-Test-Client/1.0'
        }
      });
      
      const responseTime = Date.now() - startTime;
      this.results.responseTimes.push({
        scenario: scenario.name,
        time: responseTime,
        status: response.status
      });
      
      this.results.successfulRequests++;
      
      // Log slow responses
      if (responseTime > 2000) {
        console.log(`⚠️ Slow response: ${scenario.name} - ${responseTime}ms`);
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.results.failedRequests++;
      this.results.errors.push({
        scenario: scenario.name,
        error: error.message,
        time: responseTime
      });
      
      console.log(`❌ Request failed: ${scenario.name} - ${error.message}`);
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate performance report
   */
  generateReport() {
    const duration = (this.results.endTime - this.results.startTime) / 1000;
    const successRate = (this.results.successfulRequests / this.results.totalRequests) * 100;
    
    // Calculate response time statistics
    const responseTimes = this.results.responseTimes.map(r => r.time);
    responseTimes.sort((a, b) => a - b);
    
    const stats = {
      min: responseTimes[0] || 0,
      max: responseTimes[responseTimes.length - 1] || 0,
      avg: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
      p50: this.percentile(responseTimes, 50),
      p90: this.percentile(responseTimes, 90),
      p95: this.percentile(responseTimes, 95),
      p99: this.percentile(responseTimes, 99),
    };

    // Group by scenario
    const scenarioStats = {};
    this.results.responseTimes.forEach(r => {
      if (!scenarioStats[r.scenario]) {
        scenarioStats[r.scenario] = [];
      }
      scenarioStats[r.scenario].push(r.time);
    });

    console.log('\n📊 PERFORMANCE TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`Test Duration: ${duration.toFixed(2)}s`);
    console.log(`Total Requests: ${this.results.totalRequests}`);
    console.log(`Successful: ${this.results.successfulRequests} (${successRate.toFixed(2)}%)`);
    console.log(`Failed: ${this.results.failedRequests}`);
    console.log(`Requests/sec: ${(this.results.totalRequests / duration).toFixed(2)}`);
    
    console.log('\n📈 RESPONSE TIME STATISTICS (ms)');
    console.log('-'.repeat(30));
    console.log(`Min: ${stats.min}`);
    console.log(`Max: ${stats.max}`);
    console.log(`Average: ${stats.avg.toFixed(2)}`);
    console.log(`50th percentile: ${stats.p50}`);
    console.log(`90th percentile: ${stats.p90}`);
    console.log(`95th percentile: ${stats.p95}`);
    console.log(`99th percentile: ${stats.p99}`);

    console.log('\n📋 SCENARIO BREAKDOWN');
    console.log('-'.repeat(30));
    Object.entries(scenarioStats).forEach(([scenario, times]) => {
      const avg = times.reduce((a, b) => a + b, 0) / times.length;
      const p95 = this.percentile(times, 95);
      console.log(`${scenario}: ${times.length} requests, avg: ${avg.toFixed(2)}ms, p95: ${p95}ms`);
    });

    if (this.results.errors.length > 0) {
      console.log('\n❌ ERRORS');
      console.log('-'.repeat(30));
      const errorGroups = {};
      this.results.errors.forEach(e => {
        const key = `${e.scenario}: ${e.error}`;
        errorGroups[key] = (errorGroups[key] || 0) + 1;
      });
      
      Object.entries(errorGroups).forEach(([error, count]) => {
        console.log(`${error} (${count} times)`);
      });
    }

    // Performance assessment
    console.log('\n🎯 PERFORMANCE ASSESSMENT');
    console.log('-'.repeat(30));
    
    const assessments = [];
    if (stats.avg > 1000) assessments.push('❌ High average response time');
    if (stats.p95 > 2000) assessments.push('❌ High 95th percentile response time');
    if (successRate < 99) assessments.push('❌ Low success rate');
    if (this.results.totalRequests / duration < 10) assessments.push('❌ Low throughput');
    
    if (assessments.length === 0) {
      console.log('✅ Performance looks good!');
    } else {
      assessments.forEach(assessment => console.log(assessment));
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    if (stats.avg > 500) {
      console.log('• Consider adding more database indexes');
      console.log('• Review slow queries and optimize them');
      console.log('• Implement more aggressive caching');
    }
    if (stats.p95 > 1000) {
      console.log('• Investigate outlier requests');
      console.log('• Consider request timeout optimization');
    }
    if (successRate < 99) {
      console.log('• Review error logs for failure patterns');
      console.log('• Implement better error handling');
    }
  }

  /**
   * Calculate percentile
   */
  percentile(arr, p) {
    if (arr.length === 0) return 0;
    const index = Math.ceil((p / 100) * arr.length) - 1;
    return arr[index];
  }

  /**
   * Test specific API endpoints
   */
  async testSpecificEndpoints() {
    console.log('\n🔍 Testing Specific Endpoints');
    
    const endpoints = [
      { name: 'Health Check', url: '/health' },
      { name: 'Metrics', url: '/metrics' },
      { name: 'Database Check', url: '/check-db' },
      { name: 'Products List', url: '/products?page=1&limit=10' },
      { name: 'Categories', url: '/master/categories' },
      { name: 'Currency Rates', url: '/currency-rates/current' },
    ];

    for (const endpoint of endpoints) {
      try {
        const start = Date.now();
        const response = await axios.get(`${BASE_URL}${endpoint.url}`, { timeout: 5000 });
        const responseTime = Date.now() - start;
        
        console.log(`✅ ${endpoint.name}: ${responseTime}ms (${response.status})`);
      } catch (error) {
        console.log(`❌ ${endpoint.name}: ${error.message}`);
      }
    }
  }
}

// Run the test
async function main() {
  const test = new PerformanceTest();
  
  // Test individual endpoints first
  await test.testSpecificEndpoints();
  
  // Run load test
  await test.runLoadTest();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}

module.exports = PerformanceTest;
