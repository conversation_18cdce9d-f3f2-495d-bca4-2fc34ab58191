-- CreateIndex
CREATE INDEX `Bid_amount_idx` ON `Bid`(`amount`);

-- CreateIndex
CREATE INDEX `Bid_productId_amount_idx` ON `Bid`(`productId`, `amount`);

-- CreateIndex
CREATE INDEX `Bid_productId_createdAt_idx` ON `Bid`(`productId`, `createdAt`);

-- CreateIndex
CREATE INDEX `Bid_bidderId_createdAt_idx` ON `Bid`(`bidderId`, `createdAt`);

-- CreateIndex
CREATE INDEX `Bid_productId_isWinning_idx` ON `Bid`(`productId`, `isWinning`);

-- CreateIndex
CREATE INDEX `Bid_auctionWon_winnerNotified_idx` ON `Bid`(`auctionWon`, `winnerNotified`);

-- CreateIndex
CREATE INDEX `Bid_productId_bidderId_amount_idx` ON `Bid`(`productId`, `bidderId`, `amount`);

-- CreateIndex
CREATE INDEX `CurrencyRate_fromCurrency_idx` ON `CurrencyRate`(`fromCurrency`);

-- CreateIndex
CREATE INDEX `CurrencyRate_toCurrency_idx` ON `CurrencyRate`(`toCurrency`);

-- CreateIndex
CREATE INDEX `CurrencyRate_fromCurrency_toCurrency_isActive_idx` ON `CurrencyRate`(`fromCurrency`, `toCurrency`, `isActive`);

-- CreateIndex
CREATE INDEX `CurrencyRate_isActive_date_idx` ON `CurrencyRate`(`isActive`, `date`);

-- CreateIndex
CREATE INDEX `CurrencyRate_fromCurrency_toCurrency_date_isActive_idx` ON `CurrencyRate`(`fromCurrency`, `toCurrency`, `date`, `isActive`);

-- CreateIndex
CREATE INDEX `Order_createdAt_idx` ON `Order`(`createdAt`);

-- CreateIndex
CREATE INDEX `Order_updatedAt_idx` ON `Order`(`updatedAt`);

-- CreateIndex
CREATE INDEX `Order_userId_status_createdAt_idx` ON `Order`(`userId`, `status`, `createdAt`);

-- CreateIndex
CREATE INDEX `Order_userId_paymentStatus_createdAt_idx` ON `Order`(`userId`, `paymentStatus`, `createdAt`);

-- CreateIndex
CREATE INDEX `Order_status_paymentStatus_idx` ON `Order`(`status`, `paymentStatus`);

-- CreateIndex
CREATE INDEX `Order_userId_createdAt_idx` ON `Order`(`userId`, `createdAt`);

-- CreateIndex
CREATE INDEX `Order_paymentStatus_createdAt_idx` ON `Order`(`paymentStatus`, `createdAt`);

-- CreateIndex
CREATE INDEX `Product_isActive_idx` ON `Product`(`isActive`);

-- CreateIndex
CREATE INDEX `Product_createdAt_idx` ON `Product`(`createdAt`);

-- CreateIndex
CREATE INDEX `Product_priceUSD_idx` ON `Product`(`priceUSD`);

-- CreateIndex
CREATE INDEX `Product_status_isActive_sellType_idx` ON `Product`(`status`, `isActive`, `sellType`);

-- CreateIndex
CREATE INDEX `Product_categoryId_status_isActive_idx` ON `Product`(`categoryId`, `status`, `isActive`);

-- CreateIndex
CREATE INDEX `Product_sellType_auctionEndDate_auctionCompleted_idx` ON `Product`(`sellType`, `auctionEndDate`, `auctionCompleted`);

-- CreateIndex
CREATE INDEX `Product_sellerId_status_createdAt_idx` ON `Product`(`sellerId`, `status`, `createdAt`);

-- CreateIndex
CREATE INDEX `Product_status_isActive_createdAt_idx` ON `Product`(`status`, `isActive`, `createdAt`);

-- CreateIndex
CREATE INDEX `Product_sellType_status_isActive_priceUSD_idx` ON `Product`(`sellType`, `status`, `isActive`, `priceUSD`);

-- CreateIndex
CREATE INDEX `Product_categoryId_itemTypeId_status_isActive_idx` ON `Product`(`categoryId`, `itemTypeId`, `status`, `isActive`);

-- CreateIndex
CREATE INDEX `Product_auctionCompleted_auctionEndDate_sellType_idx` ON `Product`(`auctionCompleted`, `auctionEndDate`, `sellType`);
