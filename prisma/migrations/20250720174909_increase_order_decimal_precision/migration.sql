/*
  Warnings:

  - You are about to alter the column `subtotal` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(15,2)`.
  - You are about to alter the column `shippingCost` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(15,2)`.
  - You are about to alter the column `tax` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(15,2)`.
  - You are about to alter the column `total` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(15,2)`.
  - You are about to alter the column `price` on the `OrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(15,2)`.

*/
-- AlterTable
ALTER TABLE `Order` MODIFY `subtotal` DECIMAL(15, 2) NOT NULL,
    MODIFY `shippingCost` DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
    MODIFY `tax` DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
    MODIFY `total` DECIMAL(15, 2) NOT NULL;

-- AlterTable
ALTER TABLE `OrderItem` MODIFY `price` DECIMAL(15, 2) NOT NULL;
