#!/usr/bin/env tsx

import { PrismaClient } from '../generated/client'

const prisma = new PrismaClient()

/**
 * Clean up old and duplicate data
 */
async function main() {
  try {
    console.log('🧹 Starting database maintenance and cleanup...')
    
    // 1. Clean up old currency rates (keep only last 30 days)
    console.log('🧹 Cleaning up old currency rates...')
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const deletedRates = await prisma.currencyRate.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo
        },
        isActive: false
      }
    })
    console.log(`✅ Deleted ${deletedRates.count} old currency rates`)

    // 2. Clean up duplicate currency rates for the same date
    console.log('🧹 Cleaning up duplicate currency rates...')

    try {
      // Get all active currency rates grouped by date
      const allRates = await prisma.currencyRate.findMany({
        where: {
          isActive: true
        },
        orderBy: [
          { fromCurrency: 'asc' },
          { toCurrency: 'asc' },
          { date: 'asc' },
          { createdAt: 'desc' }
        ]
      })

      // Group by fromCurrency, toCurrency, date
      const groupedRates = new Map<string, typeof allRates>()

      for (const rate of allRates) {
        const key = `${rate.fromCurrency}-${rate.toCurrency}-${rate.date.toISOString().split('T')[0]}`
        if (!groupedRates.has(key)) {
          groupedRates.set(key, [])
        }
        groupedRates.get(key)!.push(rate)
      }

      let totalDeleted = 0
      const keys = Array.from(groupedRates.keys())
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        const rates = groupedRates.get(key)!
        if (rates.length > 1) {
          // Keep the first (most recent due to ordering), delete the rest
          const toDelete = rates.slice(1)
          for (const rate of toDelete) {
            await prisma.currencyRate.delete({
              where: { id: rate.id }
            })
          }
          totalDeleted += toDelete.length
          console.log(`✅ Cleaned up ${toDelete.length} duplicate rates for ${key}`)
        }
      }

      if (totalDeleted === 0) {
        console.log('ℹ️ No duplicate currency rates found')
      } else {
        console.log(`✅ Total duplicate rates cleaned: ${totalDeleted}`)
      }
    } catch (error) {
      console.log('⚠️ Could not clean duplicate currency rates:', error instanceof Error ? error.message : 'Unknown error')
    }

    // 3. Clean up old audit logs (keep only last 90 days) - if table exists
    try {
      console.log('🧹 Cleaning up old audit logs...')
      const ninetyDaysAgo = new Date()
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)

      if ((prisma as any).auditLog) {
        const deletedLogs = await (prisma as any).auditLog.deleteMany({
          where: {
            createdAt: {
              lt: ninetyDaysAgo
            }
          }
        })
        console.log(`✅ Deleted ${deletedLogs.count} old audit logs`)
      } else {
        console.log('ℹ️ AuditLog table not available, skipping...')
      }
    } catch (error) {
      console.log('ℹ️ Could not clean audit logs (table may not exist)')
    }

    // 4. Clean up old notifications (keep only last 60 days) - if table exists
    try {
      console.log('🧹 Cleaning up old notifications...')
      const sixtyDaysAgo = new Date()
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60)

      if ((prisma as any).notification) {
        const deletedNotifications = await (prisma as any).notification.deleteMany({
          where: {
            createdAt: {
              lt: sixtyDaysAgo
            },
            isRead: true
          }
        })
        console.log(`✅ Deleted ${deletedNotifications.count} old read notifications`)
      } else {
        console.log('ℹ️ Notification table not available, skipping...')
      }
    } catch (error) {
      console.log('ℹ️ Could not clean notifications (table may not exist)')
    }

    // 5. Update statistics
    console.log('📊 Updating database statistics...')

    const stats = await Promise.all([
      prisma.user.count(),
      prisma.product.count(),
      prisma.order.count(),
      prisma.bid.count(),
      prisma.currencyRate.count({ where: { isActive: true } })
    ])

    // Optional table counts
    let notificationCount = 0
    let auditLogCount = 0

    try {
      if ((prisma as any).notification) {
        notificationCount = await (prisma as any).notification.count()
      }
    } catch (error) {
      // Table doesn't exist
    }

    try {
      if ((prisma as any).auditLog) {
        auditLogCount = await (prisma as any).auditLog.count()
      }
    } catch (error) {
      // Table doesn't exist
    }

    console.log('📊 Current database statistics:')
    console.log(`  Users: ${stats[0]}`)
    console.log(`  Products: ${stats[1]}`)
    console.log(`  Orders: ${stats[2]}`)
    console.log(`  Bids: ${stats[3]}`)
    console.log(`  Active Currency Rates: ${stats[4]}`)
    console.log(`  Notifications: ${notificationCount} ${notificationCount === 0 ? '(table not available)' : ''}`)
    console.log(`  Audit Logs: ${auditLogCount} ${auditLogCount === 0 ? '(table not available)' : ''}`)

    // 6. Optimize database (if supported)
    console.log('⚡ Running database optimization...')
    try {
      // Note: This is MySQL specific. Adjust for other databases.
      // Only optimize tables that definitely exist
      await prisma.$executeRaw`OPTIMIZE TABLE User, Product, \`Order\`, Bid, CurrencyRate`
      console.log('✅ Database optimization completed')
    } catch (error) {
      console.log('ℹ️ Database optimization skipped (not supported or not needed)')
      console.log(`   Reason: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    console.log('✅ Database maintenance completed successfully!')
    
  } catch (error) {
    console.error('❌ Database maintenance failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export default main
