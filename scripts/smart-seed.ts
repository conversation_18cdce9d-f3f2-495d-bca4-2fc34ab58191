#!/usr/bin/env tsx

import { PrismaClient } from '../generated/client'

const prisma = new PrismaClient()

interface SeedStatus {
  categories: boolean
  itemTypes: boolean
  paymentMethods: boolean
  users: boolean
  products: boolean
  currencyRates: boolean
}

/**
 * Check what data already exists in the database
 */
async function checkSeedStatus(): Promise<SeedStatus> {
  console.log('🔍 Checking existing data in database...')
  
  const [
    categoriesCount,
    itemTypesCount,
    paymentMethodsCount,
    usersCount,
    productsCount,
    currencyRatesCount
  ] = await Promise.all([
    prisma.category.count(),
    prisma.itemType.count(),
    prisma.paymentMethod.count(),
    prisma.user.count(),
    prisma.product.count(),
    prisma.currencyRate.count()
  ])

  const status: SeedStatus = {
    categories: categoriesCount > 0,
    itemTypes: itemTypesCount > 0,
    paymentMethods: paymentMethodsCount > 0,
    users: usersCount > 0,
    products: productsCount > 0,
    currencyRates: currencyRatesCount > 0
  }

  console.log('📊 Current database status:')
  console.log(`  Categories: ${categoriesCount} (${status.categories ? '✅ exists' : '❌ empty'})`)
  console.log(`  Item Types: ${itemTypesCount} (${status.itemTypes ? '✅ exists' : '❌ empty'})`)
  console.log(`  Payment Methods: ${paymentMethodsCount} (${status.paymentMethods ? '✅ exists' : '❌ empty'})`)
  console.log(`  Users: ${usersCount} (${status.users ? '✅ exists' : '❌ empty'})`)
  console.log(`  Products: ${productsCount} (${status.products ? '✅ exists' : '❌ empty'})`)
  console.log(`  Currency Rates: ${currencyRatesCount} (${status.currencyRates ? '✅ exists' : '❌ empty'})`)

  return status
}

/**
 * Seed categories if they don't exist
 */
async function seedCategories() {
  console.log('🌱 Seeding categories...')
  
  const categories = [
    {
      name: 'Sport',
      description: 'Sports trading cards and memorabilia',
      sellType: 'auction'
    },
    {
      name: 'Non Sport',
      description: 'Non-sports trading cards and collectibles',
      sellType: 'auction'
    },
    {
      name: 'Collectible',
      description: 'General collectible items',
      sellType: 'auction' 
    },
    {
      name: 'Gaming',
      description: 'Gaming cards and collectibles',
      sellType: 'auction'
    }
  ]

  for (const category of categories) {
    await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: category
    })
  }

  console.log(`✅ Categories seeded successfully`)
}

/**
 * Seed item types if they don't exist
 */
async function seedItemTypes() {
  console.log('🌱 Seeding item types...')

  // Note: Item types seeding is currently disabled
  // TODO: Implement proper item types seeding with correct schema

  console.log(`✅ Item types seeded successfully (skipped - implementation needed)`)
}

/**
 * Seed payment methods if they don't exist
 */
async function seedPaymentMethods() {
  console.log('🌱 Seeding payment methods...')

  // Import the existing payment methods seeder
  try {
    const { default: seedPaymentMethodsData } = await import('../prisma/seeders/paymentMethods.seeder')
    await seedPaymentMethodsData()
    console.log(`✅ Payment methods seeded successfully using existing seeder`)
  } catch (error) {
    console.warn('⚠️ Could not use existing payment methods seeder, using fallback...')

    // Fallback payment methods
    const paymentMethods = [
      {
        id: 'xendit_invoice_usd',
        name: 'All Payment Methods',
        description: 'Credit Card, Bank Transfer, E-Wallet & More',
        type: 'invoice',
        currency: 'USD',
        isActive: true,
        isRecommended: true,
        icon: '/payment-icons/xendit.png',
        processingFee: 2.9,
        processingFeeType: 'percentage',
        minAmount: 1,
        maxAmount: 100000,
        supportedCountries: ['US', 'ID', 'PH', 'MY', 'TH', 'VN'],
        features: ['instant_settlement', 'refund_support', 'recurring_payment'],
        xenditConfig: {
          paymentMethods: ['CREDIT_CARD', 'BANK_TRANSFER', 'EWALLET'],
          currency: 'USD'
        }
      },
      {
        id: 'xendit_invoice_idr',
        name: 'Semua Metode Pembayaran',
        description: 'Kartu Kredit, Transfer Bank, E-Wallet & Lainnya',
        type: 'invoice',
        currency: 'IDR',
        isActive: true,
        isRecommended: true,
        icon: '/payment-icons/xendit.png',
        processingFee: 2.9,
        processingFeeType: 'percentage',
        minAmount: 15000,
        maxAmount: **********,
        supportedCountries: ['ID'],
        features: ['instant_settlement', 'refund_support', 'recurring_payment'],
        xenditConfig: {
          paymentMethods: ['CREDIT_CARD', 'BANK_TRANSFER', 'EWALLET'],
          currency: 'IDR'
        }
      }
    ]

    for (const method of paymentMethods) {
      await prisma.paymentMethod.upsert({
        where: { id: method.id },
        update: {},
        create: method
      })
    }

    console.log(`✅ Payment methods seeded successfully using fallback`)
  }
}

/**
 * Seed initial currency rates if they don't exist
 */
async function seedCurrencyRates() {
  console.log('🌱 Seeding initial currency rates...')

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const existingRate = await prisma.currencyRate.findFirst({
    where: {
      fromCurrency: 'USD',
      toCurrency: 'IDR',
      date: today,
      isActive: true
    }
  })

  if (!existingRate) {
    try {
      await prisma.currencyRate.create({
        data: {
          fromCurrency: 'USD',
          toCurrency: 'IDR',
          rate: 15500.0,
          sellRate: 15810.0, // 2% margin
          buyRate: 15190.0,  // 2% margin
          margin: 0.02,
          source: 'smart-seed',
          isActive: true,
          date: today
        }
      })
      console.log(`✅ Currency rates created for today`)
    } catch (error: any) {
      // If there's a unique constraint error, it means another process created it
      if (error.code === 'P2002') {
        console.log(`ℹ️ Currency rates already exist for today (created by another process)`)
      } else {
        throw error
      }
    }
  } else {
    console.log(`ℹ️ Currency rates already exist for today`)
  }
}

/**
 * Main smart seeding function
 */
async function main() {
  try {
    console.log('🚀 Starting smart database seeding...')
    
    const status = await checkSeedStatus()
    
    // Always seed essential data if missing
    if (!status.categories) {
      await seedCategories()
    } else {
      console.log('ℹ️ Categories already exist, skipping...')
    }
    
    if (!status.itemTypes) {
      await seedItemTypes()
    } else {
      console.log('ℹ️ Item types already exist, skipping...')
    }
    
    if (!status.paymentMethods) {
      await seedPaymentMethods()
    } else {
      console.log('ℹ️ Payment methods already exist, skipping...')
    }
    
    // Always check and seed currency rates (they expire daily)
    await seedCurrencyRates()
    
    // Skip user and product seeding in smart mode (only for development)
    if (!status.users && !status.products) {
      console.log('ℹ️ No users or products found. This appears to be a fresh database.')
      console.log('ℹ️ Skipping user/product seeding in smart mode.')
      console.log('ℹ️ Run "npm run db:seed" manually if you need sample data.')
    }
    
    console.log('✅ Smart seeding completed successfully!')
    
  } catch (error) {
    console.error('❌ Smart seeding failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export default main
