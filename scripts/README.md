# Database Scripts Documentation

This directory contains scripts for database management, deployment, and maintenance.

## Scripts Overview

### 🚀 Deployment Scripts

#### `smart-seed.ts`
Intelligent seeding that checks existing data before seeding.
- **Usage**: `npm run db:seed:smart`
- **Purpose**: Safe for staging/development deployments
- **Behavior**: 
  - Checks what data already exists
  - Only seeds missing essential data
  - Skips user/product data if database has existing users
  - Always updates currency rates

#### `production-seed.ts`
Production-safe seeding for live environments.
- **Usage**: `npm run db:seed:production`
- **Purpose**: Safe for production deployments
- **Behavior**:
  - Only seeds essential system data
  - Never creates sample users or products
  - Updates existing data safely with upsert
  - Includes safety checks

#### `check-seed-status.ts`
Comprehensive database status checker.
- **Usage**: `npm run db:seed:check`
- **Purpose**: Diagnose database state
- **Output**:
  - Table counts and status
  - Health assessment
  - Seeding recommendations
  - Exit codes for automation

#### `maintenance-clean.ts`
Database cleanup and optimization.
- **Usage**: `npm run maintenance:clean`
- **Purpose**: Regular maintenance
- **Actions**:
  - Removes old currency rates (30+ days)
  - Cleans duplicate data
  - Removes old audit logs (90+ days)
  - Removes old notifications (60+ days)
  - Database optimization

## Package.json Scripts

### Development
```bash
npm run dev              # Start development server
npm run dev:full         # Start with WebSocket server
npm run db:migrate       # Run migrations in dev mode
npm run db:seed          # Full seeding with sample data
npm run db:reset         # Reset and reseed database
```

### Database Management
```bash
npm run db:generate      # Generate Prisma client
npm run db:migrate:deploy # Deploy migrations (production)
npm run db:migrate:status # Check migration status
npm run db:seed:smart    # Smart seeding (recommended)
npm run db:seed:production # Production-safe seeding
npm run db:seed:check    # Check database status
npm run db:status        # Full status check
```

### Deployment
```bash
npm run build            # Build for staging (with smart seeding)
npm run build:production # Build for production
npm run deploy:staging   # Full staging deployment
npm run deploy:production # Full production deployment
npm run postdeploy       # Post-deployment checks
```

### Maintenance
```bash
npm run maintenance:clean # Database cleanup
npm run clean-currency-duplicates # Legacy cleanup
```

## Deployment Workflow

### Staging Deployment
```bash
npm run deploy:staging
```
This runs:
1. `db:deploy` (generate + migrate + smart seed)
2. `build` (Next.js build)
3. `start` (Start application)

### Production Deployment
```bash
npm run deploy:production
```
This runs:
1. `db:deploy:production` (generate + migrate + production seed)
2. `build:production` (Next.js build)

### Post-Deployment
```bash
npm run postdeploy
```
This runs:
1. `db:status` (migration status + seed check)

## Exit Codes

Scripts use standard exit codes for automation:
- `0`: Success
- `1`: Error (deployment should fail)
- `2`: Warning (deployment can continue but needs attention)

## Best Practices

### For Staging
1. Use `npm run deploy:staging` for full deployment
2. Use `npm run db:seed:smart` for safe seeding
3. Check status with `npm run db:status`

### For Production
1. Use `npm run deploy:production` for deployment
2. Never use full seeding in production
3. Run `npm run postdeploy` to verify
4. Schedule `npm run maintenance:clean` weekly

### For Development
1. Use `npm run db:reset` to start fresh
2. Use `npm run db:seed` for sample data
3. Use `npm run dev:full` for complete development setup

## Troubleshooting

### Migration Conflicts
```bash
npm run db:migrate:status  # Check current state
npm run db:migrate:reset   # Reset if needed (DEV ONLY)
```

### Seeding Issues
```bash
npm run db:seed:check      # Diagnose issues
npm run db:seed:force      # Force reseed (DEV ONLY)
```

### Database Cleanup
```bash
npm run maintenance:clean  # Clean old data
npm run clean-currency-duplicates # Fix currency issues
```

## Environment Variables

Ensure these are set for production:
- `DATABASE_URL`: Production database connection
- `NODE_ENV=production`: Production mode
- Other app-specific variables

## Safety Features

1. **Production Protection**: Production scripts never create sample data
2. **Conflict Prevention**: Smart seeding checks existing data
3. **Rollback Safety**: Migrations are reversible
4. **Health Checks**: Status scripts verify deployment success
5. **Cleanup Automation**: Maintenance scripts prevent data bloat

## Monitoring

Use these commands for monitoring:
```bash
npm run db:status          # Quick health check
npm run db:seed:check      # Detailed status
npm run maintenance:clean  # Weekly cleanup
```
