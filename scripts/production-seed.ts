#!/usr/bin/env tsx

import { PrismaClient } from '../generated/client'

const prisma = new PrismaClient()

/**
 * Production-safe seeding - only essential data, no sample data
 */
async function main() {
  try {
    console.log('🚀 Starting production database seeding...')
    
    // Check if this is a fresh database
    const [categoriesCount, usersCount] = await Promise.all([
      prisma.category.count(),
      prisma.user.count()
    ])
    
    if (usersCount > 0) {
      console.log('⚠️ Database contains user data. Skipping production seeding to prevent data conflicts.')
      console.log('ℹ️ Only essential system data will be updated.')
    }
    
    // 1. Seed Categories (essential for app functionality)
    console.log('🌱 Ensuring categories exist...')
    const categories = [
      {
        name: 'Sport',
        description: 'Sports trading cards and memorabilia',
        sellType: 'auction'
      },
      {
        name: 'Non Sport',
        description: 'Non-sports trading cards and collectibles',
        sellType: 'auction'
      },
      {
        name: 'Collectible',
        description: 'General collectible items',
        sellType: 'auction' 
      },
      {
        name: 'Gaming',
        description: 'Gaming cards and collectibles',
        sellType: 'auction'
      }
    ]

    for (const category of categories) {
      await prisma.category.upsert({
        where: { name: category.name },
        update: {
          description: category.description,
          sellType: category.sellType
        },
        create: category
      })
    }
    console.log(`✅ Categories ensured (${categories.length} categories)`)

    // 2. Seed Item Types (essential for app functionality)
    console.log('🌱 Ensuring item types exist...')
    const categoriesFromDb = await prisma.category.findMany()
    
    const itemTypesData = [
      // Sport category items
      { name: 'Baseball Cards', categoryName: 'Sport' },
      { name: 'Basketball Cards', categoryName: 'Sport' },
      { name: 'Football Cards', categoryName: 'Sport' },
      { name: 'Soccer Cards', categoryName: 'Sport' },
      { name: 'Hockey Cards', categoryName: 'Sport' },
      
      // Non Sport category items
      { name: 'Entertainment Cards', categoryName: 'Non Sport' },
      { name: 'Movie Cards', categoryName: 'Non Sport' },
      { name: 'TV Show Cards', categoryName: 'Non Sport' },
      { name: 'Music Cards', categoryName: 'Non Sport' },
      
      // Collectible category items
      { name: 'Vintage Items', categoryName: 'Collectible' },
      { name: 'Rare Collectibles', categoryName: 'Collectible' },
      { name: 'Limited Edition', categoryName: 'Collectible' },
      { name: 'Autographed Items', categoryName: 'Collectible' },
      
      // Gaming category items
      { name: 'Pokemon Cards', categoryName: 'Gaming' },
      { name: 'Magic Cards', categoryName: 'Gaming' },
      { name: 'Yu-Gi-Oh Cards', categoryName: 'Gaming' },
      { name: 'Gaming Accessories', categoryName: 'Gaming' }
    ]

    let itemTypesCreated = 0
    for (const itemTypeData of itemTypesData) {
      const category = categoriesFromDb.find(c => c.name === itemTypeData.categoryName)
      if (category) {
        await prisma.itemType.upsert({
          where: { 
            name_categoryId: {
              name: itemTypeData.name,
              categoryId: category.id
            }
          },
          update: {},
          create: {
            name: itemTypeData.name,
            categoryId: category.id
          }
        })
        itemTypesCreated++
      }
    }
    console.log(`✅ Item types ensured (${itemTypesCreated} item types)`)

    // 3. Seed Payment Methods (essential for transactions)
    console.log('🌱 Ensuring payment methods exist...')
    const paymentMethods = [
      {
        name: 'Credit Card',
        code: 'CREDIT_CARD',
        description: 'Pay with credit card',
        isActive: true,
        processingFee: 2.9,
        currency: 'USD'
      },
      {
        name: 'Bank Transfer',
        code: 'BANK_TRANSFER',
        description: 'Pay via bank transfer',
        isActive: true,
        processingFee: 0.0,
        currency: 'USD'
      },
      {
        name: 'Digital Wallet',
        code: 'DIGITAL_WALLET',
        description: 'Pay with digital wallet',
        isActive: true,
        processingFee: 1.5,
        currency: 'USD'
      }
    ]

    for (const method of paymentMethods) {
      await prisma.paymentMethod.upsert({
        where: { code: method.code },
        update: {
          name: method.name,
          description: method.description,
          isActive: method.isActive,
          processingFee: method.processingFee,
          currency: method.currency
        },
        create: method
      })
    }
    console.log(`✅ Payment methods ensured (${paymentMethods.length} methods)`)

    // 4. Ensure current currency rates exist
    console.log('🌱 Ensuring currency rates exist...')
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const existingRate = await prisma.currencyRate.findFirst({
      where: {
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        date: today,
        isActive: true
      }
    })

    if (!existingRate) {
      try {
        await prisma.currencyRate.create({
          data: {
            fromCurrency: 'USD',
            toCurrency: 'IDR',
            rate: 15500.0,
            sellRate: 15810.0, // 2% margin
            buyRate: 15190.0,  // 2% margin
            margin: 0.02,
            source: 'production-seed',
            isActive: true,
            date: today
          }
        })
        console.log(`✅ Currency rates created for today`)
      } catch (error) {
        // If there's a unique constraint error, it means another process created it
        if (error.code === 'P2002') {
          console.log(`ℹ️ Currency rates already exist for today (created by another process)`)
        } else {
          throw error
        }
      }
    } else {
      console.log(`ℹ️ Currency rates already exist for today`)
    }

    // 5. Production safety checks
    console.log('🔍 Running production safety checks...')
    
    const finalCounts = await Promise.all([
      prisma.category.count(),
      prisma.itemType.count(),
      prisma.paymentMethod.count(),
      prisma.currencyRate.count({ where: { isActive: true } })
    ])

    console.log('📊 Final database status:')
    console.log(`  Categories: ${finalCounts[0]}`)
    console.log(`  Item Types: ${finalCounts[1]}`)
    console.log(`  Payment Methods: ${finalCounts[2]}`)
    console.log(`  Active Currency Rates: ${finalCounts[3]}`)

    // Verify essential data exists
    if (finalCounts[0] === 0 || finalCounts[1] === 0 || finalCounts[2] === 0) {
      throw new Error('Essential data missing after seeding!')
    }

    console.log('✅ Production seeding completed successfully!')
    console.log('ℹ️ Only essential system data was seeded. No sample users or products were created.')
    
  } catch (error) {
    console.error('❌ Production seeding failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export default main
