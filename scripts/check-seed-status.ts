#!/usr/bin/env tsx

import { PrismaClient } from '../generated/client'

const prisma = new PrismaClient()

interface DatabaseStatus {
  tables: {
    [key: string]: {
      count: number
      status: 'empty' | 'has_data' | 'error'
      lastUpdated?: Date
    }
  }
  migrations: {
    applied: number
    pending: number
    status: 'up_to_date' | 'pending' | 'error'
  }
  health: 'healthy' | 'warning' | 'error'
}

/**
 * Check the status of all database tables
 */
async function checkDatabaseStatus(): Promise<DatabaseStatus> {
  console.log('🔍 Checking database status...')
  
  const status: DatabaseStatus = {
    tables: {},
    migrations: {
      applied: 0,
      pending: 0,
      status: 'up_to_date'
    },
    health: 'healthy'
  }

  try {
    // Check all main tables
    const tableChecks = [
      { name: 'categories', query: () => prisma.category.count() },
      { name: 'itemTypes', query: () => prisma.itemType.count() },
      { name: 'users', query: () => prisma.user.count() },
      { name: 'products', query: () => prisma.product.count() },
      { name: 'orders', query: () => prisma.order.count() },
      { name: 'bids', query: () => prisma.bid.count() },
      { name: 'paymentMethods', query: () => prisma.paymentMethod.count() },
      { name: 'currencyRates', query: () => prisma.currencyRate.count() },
      { name: 'activeCurrencyRates', query: () => prisma.currencyRate.count({ where: { isActive: true } }) }
    ]

    // Optional tables (may not exist in all schemas)
    const optionalTableChecks = [
      { name: 'notifications', query: () => (prisma as any).notification?.count?.() },
      { name: 'auditLogs', query: () => (prisma as any).auditLog?.count?.() }
    ]

    // Check main tables
    for (const table of tableChecks) {
      try {
        const count = await table.query()
        status.tables[table.name] = {
          count,
          status: count > 0 ? 'has_data' : 'empty'
        }
      } catch (error) {
        status.tables[table.name] = {
          count: 0,
          status: 'error'
        }
        status.health = 'error'
        console.error(`❌ Error checking ${table.name}:`, error)
      }
    }

    // Check optional tables
    for (const table of optionalTableChecks) {
      try {
        const count = await table.query()
        if (count !== undefined) {
          status.tables[table.name] = {
            count,
            status: count > 0 ? 'has_data' : 'empty'
          }
        } else {
          status.tables[table.name] = {
            count: 0,
            status: 'empty'
          }
        }
      } catch (error) {
        // Optional tables - don't mark as error if they don't exist
        status.tables[table.name] = {
          count: 0,
          status: 'empty'
        }
        console.log(`ℹ️ Optional table ${table.name} not available`)
      }
    }

    // Check for recent data
    try {
      const recentCurrencyRate = await prisma.currencyRate.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      })
      
      if (recentCurrencyRate) {
        status.tables.activeCurrencyRates.lastUpdated = recentCurrencyRate.createdAt
      }
    } catch (error) {
      console.warn('⚠️ Could not check recent currency rates:', error)
    }

  } catch (error) {
    console.error('❌ Database connection error:', error)
    status.health = 'error'
  }

  return status
}

/**
 * Display database status in a readable format
 */
function displayStatus(status: DatabaseStatus) {
  console.log('\n📊 DATABASE STATUS REPORT')
  console.log('=' .repeat(50))
  
  // Health status
  const healthIcon = status.health === 'healthy' ? '✅' : status.health === 'warning' ? '⚠️' : '❌'
  console.log(`Overall Health: ${healthIcon} ${status.health.toUpperCase()}`)
  console.log('')

  // Essential tables (required for app to function)
  console.log('🔧 ESSENTIAL TABLES:')
  const essentialTables = ['categories', 'itemTypes', 'paymentMethods', 'activeCurrencyRates']
  
  for (const tableName of essentialTables) {
    const table = status.tables[tableName]
    if (table) {
      const icon = table.status === 'has_data' ? '✅' : table.status === 'empty' ? '⚠️' : '❌'
      const statusText = table.status === 'has_data' ? 'OK' : table.status === 'empty' ? 'EMPTY' : 'ERROR'
      console.log(`  ${icon} ${tableName.padEnd(20)} ${table.count.toString().padStart(6)} rows - ${statusText}`)
      
      if (table.lastUpdated) {
        console.log(`      Last updated: ${table.lastUpdated.toISOString()}`)
      }
    }
  }

  // Data tables
  console.log('\n📦 DATA TABLES:')
  const dataTables = ['users', 'products', 'orders', 'bids']

  for (const tableName of dataTables) {
    const table = status.tables[tableName]
    if (table) {
      const icon = table.status === 'has_data' ? '📊' : table.status === 'empty' ? '📭' : '❌'
      const statusText = table.status === 'has_data' ? 'HAS DATA' : table.status === 'empty' ? 'EMPTY' : 'ERROR'
      console.log(`  ${icon} ${tableName.padEnd(20)} ${table.count.toString().padStart(6)} rows - ${statusText}`)
    }
  }

  // System tables
  console.log('\n🔧 SYSTEM TABLES:')
  const systemTables = ['currencyRates']

  for (const tableName of systemTables) {
    const table = status.tables[tableName]
    if (table) {
      const icon = table.status === 'has_data' ? '📋' : table.status === 'empty' ? '📭' : '❌'
      console.log(`  ${icon} ${tableName.padEnd(20)} ${table.count.toString().padStart(6)} rows`)
    }
  }

  // Optional tables
  console.log('\n📋 OPTIONAL TABLES:')
  const optionalTables = ['notifications', 'auditLogs']

  for (const tableName of optionalTables) {
    const table = status.tables[tableName]
    if (table) {
      const icon = table.status === 'has_data' ? '📋' : table.status === 'empty' ? '📭' : '❌'
      const statusText = table.status === 'empty' ? 'NOT AVAILABLE' : table.status === 'has_data' ? 'AVAILABLE' : 'ERROR'
      console.log(`  ${icon} ${tableName.padEnd(20)} ${table.count.toString().padStart(6)} rows - ${statusText}`)
    }
  }

  console.log('\n' + '=' .repeat(50))
}

/**
 * Check if seeding is needed
 */
function checkSeedingNeeded(status: DatabaseStatus): { needed: boolean; reasons: string[] } {
  const reasons: string[] = []
  
  // Check essential tables
  if (status.tables.categories?.count === 0) {
    reasons.push('Categories table is empty')
  }
  
  if (status.tables.itemTypes?.count === 0) {
    reasons.push('Item types table is empty')
  }
  
  if (status.tables.paymentMethods?.count === 0) {
    reasons.push('Payment methods table is empty')
  }
  
  if (status.tables.activeCurrencyRates?.count === 0) {
    reasons.push('No active currency rates found')
  }

  // Check if currency rates are recent (within last 7 days)
  const currencyTable = status.tables.activeCurrencyRates
  if (currencyTable?.lastUpdated) {
    const daysSinceUpdate = (Date.now() - currencyTable.lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate > 7) {
      reasons.push('Currency rates are older than 7 days')
    }
  }

  return {
    needed: reasons.length > 0,
    reasons
  }
}

/**
 * Main function
 */
async function main() {
  try {
    const status = await checkDatabaseStatus()
    displayStatus(status)
    
    const seedCheck = checkSeedingNeeded(status)
    
    if (seedCheck.needed) {
      console.log('\n⚠️ SEEDING RECOMMENDED:')
      for (const reason of seedCheck.reasons) {
        console.log(`  • ${reason}`)
      }
      console.log('\nRun one of these commands:')
      console.log('  npm run db:seed:smart     # Smart seeding (recommended)')
      console.log('  npm run db:seed           # Full seeding with sample data')
      console.log('  npm run db:seed:production # Production-safe seeding only')
    } else {
      console.log('\n✅ Database appears to be properly seeded!')
    }

    // Exit with appropriate code
    if (status.health === 'error') {
      process.exit(1)
    } else if (status.health === 'warning' || seedCheck.needed) {
      process.exit(2) // Warning exit code
    } else {
      process.exit(0) // Success
    }
    
  } catch (error) {
    console.error('❌ Failed to check database status:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export default main
