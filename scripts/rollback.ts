#!/usr/bin/env tsx

import { PrismaClient } from '../generated/client'
import { execSync } from 'child_process'

const prisma = new PrismaClient()

interface RollbackOptions {
  migrations?: boolean
  data?: boolean
  dryRun?: boolean
  force?: boolean
}

/**
 * Get the last successful migration
 */
async function getLastMigration(): Promise<string | null> {
  try {
    const output = execSync('npx prisma migrate status', { encoding: 'utf8' })
    const lines = output.split('\n')
    
    // Find applied migrations
    const appliedMigrations = lines
      .filter(line => line.includes('✓') || line.includes('Applied'))
      .map(line => line.trim())
    
    if (appliedMigrations.length > 0) {
      // Get the last applied migration
      const lastMigration = appliedMigrations[appliedMigrations.length - 1]
      console.log(`Last applied migration: ${lastMigration}`)
      return lastMigration
    }
    
    return null
  } catch (error) {
    console.error('Could not get migration status:', error)
    return null
  }
}

/**
 * Create a backup of current data
 */
async function createDataBackup(): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const backupFile = `backup-${timestamp}.json`
  
  console.log(`📦 Creating data backup: ${backupFile}`)
  
  try {
    // Get essential data
    const [categories, itemTypes, paymentMethods, users] = await Promise.all([
      prisma.category.findMany(),
      prisma.itemType.findMany(),
      prisma.paymentMethod.findMany(),
      prisma.user.findMany({
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true
        }
      })
    ])
    
    const backup = {
      timestamp: new Date().toISOString(),
      data: {
        categories,
        itemTypes,
        paymentMethods,
        users
      }
    }
    
    // Save backup (in production, this should go to external storage)
    const fs = require('fs')
    fs.writeFileSync(`backups/${backupFile}`, JSON.stringify(backup, null, 2))
    
    console.log(`✅ Backup created: ${backupFile}`)
    return backupFile
    
  } catch (error) {
    console.error('❌ Failed to create backup:', error)
    throw error
  }
}

/**
 * Rollback migrations
 */
async function rollbackMigrations(options: RollbackOptions): Promise<void> {
  console.log('🔄 Rolling back migrations...')
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN: Would rollback migrations')
    return
  }
  
  try {
    // In Prisma, we can't easily rollback migrations
    // Instead, we reset and re-apply up to a certain point
    console.log('⚠️ Prisma does not support automatic migration rollback')
    console.log('ℹ️ Manual intervention required:')
    console.log('  1. Backup current database')
    console.log('  2. Reset database: npm run db:migrate:reset')
    console.log('  3. Apply migrations up to desired point')
    console.log('  4. Restore data from backup')
    
    if (options.force) {
      console.log('🚨 FORCE MODE: Resetting database...')
      execSync('npm run db:migrate:reset', { stdio: 'inherit' })
      console.log('✅ Database reset completed')
    }
    
  } catch (error) {
    console.error('❌ Migration rollback failed:', error)
    throw error
  }
}

/**
 * Rollback data changes
 */
async function rollbackData(options: RollbackOptions): Promise<void> {
  console.log('🔄 Rolling back data changes...')
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN: Would rollback recent data changes')
    return
  }
  
  try {
    // Remove data created in the last hour (deployment window)
    const oneHourAgo = new Date()
    oneHourAgo.setHours(oneHourAgo.getHours() - 1)
    
    console.log(`🗑️ Removing data created after ${oneHourAgo.toISOString()}`)
    
    // Remove recent products (if any were created during deployment)
    const deletedProducts = await prisma.product.deleteMany({
      where: {
        createdAt: {
          gte: oneHourAgo
        }
      }
    })
    console.log(`  Removed ${deletedProducts.count} recent products`)
    
    // Remove recent orders
    const deletedOrders = await prisma.order.deleteMany({
      where: {
        createdAt: {
          gte: oneHourAgo
        }
      }
    })
    console.log(`  Removed ${deletedOrders.count} recent orders`)
    
    // Remove recent currency rates (keep only the most recent one per day)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const recentRates = await prisma.currencyRate.findMany({
      where: {
        date: today,
        createdAt: {
          gte: oneHourAgo
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    if (recentRates.length > 1) {
      // Keep the first (most recent), delete the rest
      const ratesToDelete = recentRates.slice(1)
      for (const rate of ratesToDelete) {
        await prisma.currencyRate.delete({
          where: { id: rate.id }
        })
      }
      console.log(`  Removed ${ratesToDelete.length} duplicate currency rates`)
    }
    
    console.log('✅ Data rollback completed')
    
  } catch (error) {
    console.error('❌ Data rollback failed:', error)
    throw error
  }
}

/**
 * Verify system health after rollback
 */
async function verifyHealth(): Promise<boolean> {
  console.log('🔍 Verifying system health...')
  
  try {
    // Check database connection
    await prisma.$connect()
    console.log('  ✅ Database connection OK')
    
    // Check essential data
    const [categoriesCount, paymentMethodsCount] = await Promise.all([
      prisma.category.count(),
      prisma.paymentMethod.count()
    ])
    
    if (categoriesCount === 0 || paymentMethodsCount === 0) {
      console.log('  ⚠️ Essential data missing, running emergency seed...')
      execSync('npm run db:seed:production', { stdio: 'inherit' })
    }
    
    console.log('  ✅ Essential data OK')
    console.log('✅ System health verified')
    return true
    
  } catch (error) {
    console.error('❌ Health check failed:', error)
    return false
  }
}

/**
 * Main rollback function
 */
async function main() {
  const args = process.argv.slice(2)
  const options: RollbackOptions = {
    migrations: args.includes('--migrations'),
    data: args.includes('--data'),
    dryRun: args.includes('--dry-run'),
    force: args.includes('--force')
  }
  
  // Default to data rollback if no specific option
  if (!options.migrations && !options.data) {
    options.data = true
  }
  
  try {
    console.log('🚨 STARTING ROLLBACK PROCEDURE')
    console.log('=' .repeat(50))
    console.log(`Options: ${JSON.stringify(options, null, 2)}`)
    
    if (!options.dryRun && !options.force) {
      console.log('⚠️ This will modify your database!')
      console.log('⚠️ Add --dry-run to see what would happen')
      console.log('⚠️ Add --force to proceed anyway')
      process.exit(1)
    }
    
    // Create backup before rollback
    if (!options.dryRun) {
      await createDataBackup()
    }
    
    // Perform rollback
    if (options.migrations) {
      await rollbackMigrations(options)
    }
    
    if (options.data) {
      await rollbackData(options)
    }
    
    // Verify health
    if (!options.dryRun) {
      const healthy = await verifyHealth()
      if (!healthy) {
        console.error('❌ System health check failed after rollback')
        process.exit(1)
      }
    }
    
    console.log('✅ Rollback completed successfully')
    
  } catch (error) {
    console.error('❌ Rollback failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

export default main
